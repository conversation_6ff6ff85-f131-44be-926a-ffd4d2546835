<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1976d2;
            text-align: center;
            margin-bottom: 30px;
        }
        .cards {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 30px;
        }
        .card {
            flex: 1;
            min-width: 250px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .card h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .card .value {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }
        .sales { color: #1976d2; }
        .purchases { color: #dc004e; }
        .inventory { color: #4caf50; }
        .customers { color: #ff9800; }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            margin-top: 30px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 نظام الإدارة المتكامل</h1>
        <p style="text-align: center; font-size: 18px;">مرحباً بك في نظام إدارة الأعمال الشامل</p>
        
        <div class="cards">
            <div class="card">
                <h3>📊 المبيعات اليومية</h3>
                <div class="value sales">25,430 ر.س</div>
                <p>إجمالي مبيعات اليوم</p>
            </div>
            
            <div class="card">
                <h3>🛒 المشتريات</h3>
                <div class="value purchases">18,250 ر.س</div>
                <p>إجمالي المشتريات</p>
            </div>
            
            <div class="card">
                <h3>📦 المخزون</h3>
                <div class="value inventory">125,800 ر.س</div>
                <p>قيمة المخزون الحالي</p>
            </div>
            
            <div class="card">
                <h3>👥 العملاء</h3>
                <div class="value customers">1,245</div>
                <p>عدد العملاء المسجلين</p>
            </div>
        </div>
        
        <div class="success">
            <strong>✅ النظام يعمل بنجاح!</strong><br>
            جميع الخدمات متاحة ومتصلة بشكل صحيح
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <p><strong>المميزات المتاحة:</strong></p>
            <ul style="text-align: right; max-width: 600px; margin: 20px auto;">
                <li>إدارة المبيعات والفواتير</li>
                <li>إدارة المشتريات وأوامر الشراء</li>
                <li>إدارة المخازن والمخزون</li>
                <li>إدارة التصنيع والإنتاج</li>
                <li>إدارة العملاء والموردين</li>
                <li>التقارير والتحليلات</li>
                <li>دعم وحدات قياس متعددة</li>
                <li>نظام فئات هرمي للمنتجات</li>
            </ul>
        </div>
    </div>
    
    <script>
        console.log('النظام يعمل بنجاح!');
        
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
                this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
        
        // عرض رسالة ترحيب
        setTimeout(() => {
            alert('مرحباً بك في نظام الإدارة المتكامل! 🎉');
        }, 1000);
    </script>
</body>
</html>

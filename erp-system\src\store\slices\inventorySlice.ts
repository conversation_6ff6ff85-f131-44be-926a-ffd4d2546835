import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { StockLevel, StockMovement, Warehouse, PaginatedResponse } from '../../types';

interface InventoryState {
  stockLevels: StockLevel[];
  stockMovements: StockMovement[];
  warehouses: Warehouse[];
  loading: boolean;
  error: string | null;
  filters: {
    warehouseId: string;
    productId: string;
    movementType: string;
  };
}

const initialState: InventoryState = {
  stockLevels: [],
  stockMovements: [],
  warehouses: [],
  loading: false,
  error: null,
  filters: {
    warehouseId: '',
    productId: '',
    movementType: '',
  },
};

export const fetchStockLevels = createAsyncThunk(
  'inventory/fetchStockLevels',
  async () => {
    // Mock API call
    const mockStockLevels: StockLevel[] = [
      {
        productId: '1',
        warehouseId: '1',
        quantity: 100,
        reservedQuantity: 10,
        availableQuantity: 90,
        averageCost: 80,
      },
    ];
    return mockStockLevels;
  }
);

export const fetchWarehouses = createAsyncThunk(
  'inventory/fetchWarehouses',
  async () => {
    // Mock API call
    const mockWarehouses: Warehouse[] = [
      {
        id: '1',
        name: 'المخزن الرئيسي',
        location: 'الرياض',
        isActive: true,
      },
      {
        id: '2',
        name: 'مخزن فرعي',
        location: 'جدة',
        isActive: true,
      },
    ];
    return mockWarehouses;
  }
);

const inventorySlice = createSlice({
  name: 'inventory',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<InventoryState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchStockLevels.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStockLevels.fulfilled, (state, action) => {
        state.loading = false;
        state.stockLevels = action.payload;
      })
      .addCase(fetchStockLevels.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'فشل في تحميل مستويات المخزون';
      })
      .addCase(fetchWarehouses.fulfilled, (state, action) => {
        state.warehouses = action.payload;
      });
  },
});

export const { setFilters, clearError } = inventorySlice.actions;
export default inventorySlice.reducer;

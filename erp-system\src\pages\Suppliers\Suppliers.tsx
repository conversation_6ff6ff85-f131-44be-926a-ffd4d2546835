import React, { useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchSuppliers } from '../../store/slices/suppliersSlice';

const Suppliers: React.FC = () => {
  const dispatch = useDispatch();
  const { suppliers, loading, pagination } = useSelector((state: RootState) => state.suppliers);

  useEffect(() => {
    dispatch(fetchSuppliers({}) as any);
  }, [dispatch]);

  const columns: GridColDef[] = [
    { field: 'code', headerName: 'الكود', width: 120 },
    { field: 'name', headerName: 'اسم المورد', width: 200 },
    { field: 'email', headerName: 'البريد الإلكتروني', width: 200 },
    { field: 'phone', headerName: 'الهاتف', width: 150 },
    { field: 'city', headerName: 'المدينة', width: 120 },
    {
      field: 'currentBalance',
      headerName: 'الرصيد الحالي',
      width: 150,
      valueFormatter: (params) => `${params.value} ر.س`,
    },
    {
      field: 'isActive',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      ),
    },
  ];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">إدارة الموردين</Typography>
        <Button variant="contained" startIcon={<AddIcon />}>
          إضافة مورد جديد
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="البحث"
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Suppliers Table */}
      <Card>
        <DataGrid
          rows={suppliers}
          columns={columns}
          loading={loading}
          autoHeight
          disableSelectionOnClick
          paginationMode="server"
          rowCount={pagination.total}
          page={pagination.page - 1}
          pageSize={pagination.limit}
        />
      </Card>
    </Box>
  );
};

export default Suppliers;

import React, { useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchPurchaseOrders } from '../../store/slices/purchasesSlice';

const Purchases: React.FC = () => {
  const dispatch = useDispatch();
  const { orders, loading, pagination } = useSelector((state: RootState) => state.purchases);

  useEffect(() => {
    dispatch(fetchPurchaseOrders({}) as any);
  }, [dispatch]);

  const columns: GridColDef[] = [
    { field: 'orderNumber', headerName: 'رقم الطلب', width: 150 },
    { field: 'supplierId', headerName: 'المورد', width: 200 },
    {
      field: 'date',
      headerName: 'التاريخ',
      width: 150,
      valueFormatter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
    {
      field: 'totalAmount',
      headerName: 'المبلغ الإجمالي',
      width: 150,
      valueFormatter: (params) => `${params.value} ر.س`,
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params) => {
        const statusColors = {
          draft: 'default',
          confirmed: 'primary',
          received: 'success',
          cancelled: 'error',
        } as const;
        
        const statusLabels = {
          draft: 'مسودة',
          confirmed: 'مؤكد',
          received: 'مستلم',
          cancelled: 'ملغي',
        } as const;

        return (
          <Chip
            label={statusLabels[params.value as keyof typeof statusLabels]}
            color={statusColors[params.value as keyof typeof statusColors]}
            size="small"
          />
        );
      },
    },
  ];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">إدارة المشتريات</Typography>
        <Button variant="contained" startIcon={<AddIcon />}>
          أمر شراء جديد
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="البحث"
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>الحالة</InputLabel>
                <Select label="الحالة">
                  <MenuItem value="">جميع الحالات</MenuItem>
                  <MenuItem value="draft">مسودة</MenuItem>
                  <MenuItem value="confirmed">مؤكد</MenuItem>
                  <MenuItem value="received">مستلم</MenuItem>
                  <MenuItem value="cancelled">ملغي</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="من تاريخ"
                type="date"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="إلى تاريخ"
                type="date"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Purchase Orders Table */}
      <Card>
        <DataGrid
          rows={orders}
          columns={columns}
          loading={loading}
          autoHeight
          disableSelectionOnClick
          paginationMode="server"
          rowCount={pagination.total}
          page={pagination.page - 1}
          pageSize={pagination.limit}
        />
      </Card>
    </Box>
  );
};

export default Purchases;

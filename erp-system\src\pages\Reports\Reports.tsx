import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Typo<PERSON>,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
} from '@mui/material';
import {
  GetApp as DownloadIcon,
  Assessment as ReportIcon,
} from '@mui/icons-material';

const Reports: React.FC = () => {
  const reportTypes = [
    { id: 'sales', name: 'تقرير المبيعات', description: 'تقرير شامل عن المبيعات والإيرادات' },
    { id: 'purchases', name: 'تقرير المشتريات', description: 'تقرير شامل عن المشتريات والمصروفات' },
    { id: 'inventory', name: 'تقرير المخزون', description: 'تقرير حالة المخزون والحركات' },
    { id: 'customers', name: 'تقرير العملاء', description: 'تقرير أرصدة ومعاملات العملاء' },
    { id: 'suppliers', name: 'تقرير الموردين', description: 'تقرير أرصدة ومعاملات الموردين' },
    { id: 'financial', name: 'التقرير المالي', description: 'تقرير الأرباح والخسائر والميزانية' },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        التقارير والتحليلات
      </Typography>

      {/* Report Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            فلاتر التقارير
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="من تاريخ"
                type="date"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="إلى تاريخ"
                type="date"
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>المخزن</InputLabel>
                <Select label="المخزن">
                  <MenuItem value="">جميع المخازن</MenuItem>
                  <MenuItem value="1">المخزن الرئيسي</MenuItem>
                  <MenuItem value="2">مخزن فرعي</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>الفئة</InputLabel>
                <Select label="الفئة">
                  <MenuItem value="">جميع الفئات</MenuItem>
                  <MenuItem value="1">فئة 1</MenuItem>
                  <MenuItem value="2">فئة 2</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Available Reports */}
      <Grid container spacing={3}>
        {reportTypes.map((report) => (
          <Grid item xs={12} md={6} lg={4} key={report.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <ReportIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">{report.name}</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" mb={3}>
                  {report.description}
                </Typography>
                <Box display="flex" gap={1}>
                  <Button
                    variant="contained"
                    size="small"
                    startIcon={<DownloadIcon />}
                    fullWidth
                  >
                    تصدير PDF
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<DownloadIcon />}
                    fullWidth
                  >
                    تصدير Excel
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Quick Stats */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            إحصائيات سريعة
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="primary">
                  125,430
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  إجمالي المبيعات (ر.س)
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="secondary">
                  85,250
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  إجمالي المشتريات (ر.س)
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="success.main">
                  40,180
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  صافي الربح (ر.س)
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box textAlign="center">
                <Typography variant="h4" color="warning.main">
                  1,245
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  عدد المعاملات
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Reports;

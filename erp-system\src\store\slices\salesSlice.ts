import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SalesInvoice, PaginatedResponse } from '../../types';

interface SalesState {
  invoices: SalesInvoice[];
  loading: boolean;
  error: string | null;
  selectedInvoice: SalesInvoice | null;
  filters: {
    search: string;
    status: string;
    customerId: string;
    dateFrom: string;
    dateTo: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: SalesState = {
  invoices: [],
  loading: false,
  error: null,
  selectedInvoice: null,
  filters: {
    search: '',
    status: '',
    customerId: '',
    dateFrom: '',
    dateTo: '',
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
};

export const fetchSalesInvoices = createAsyncThunk(
  'sales/fetchSalesInvoices',
  async (params: any) => {
    // Mock API call
    const mockInvoices: SalesInvoice[] = [
      {
        id: '1',
        invoiceNumber: 'INV-001',
        customerId: '1',
        date: new Date(),
        subtotal: 1000,
        taxAmount: 150,
        discountAmount: 50,
        totalAmount: 1100,
        paidAmount: 1100,
        status: 'paid',
        items: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    return {
      data: mockInvoices,
      total: mockInvoices.length,
      page: params.page || 1,
      limit: params.limit || 10,
      totalPages: Math.ceil(mockInvoices.length / (params.limit || 10)),
    } as PaginatedResponse<SalesInvoice>;
  }
);

const salesSlice = createSlice({
  name: 'sales',
  initialState,
  reducers: {
    setSelectedInvoice: (state, action: PayloadAction<SalesInvoice | null>) => {
      state.selectedInvoice = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<SalesState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action: PayloadAction<Partial<SalesState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSalesInvoices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSalesInvoices.fulfilled, (state, action) => {
        state.loading = false;
        state.invoices = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          limit: action.payload.limit,
          total: action.payload.total,
        };
      })
      .addCase(fetchSalesInvoices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'فشل في تحميل فواتير المبيعات';
      });
  },
});

export const { setSelectedInvoice, setFilters, setPagination, clearError } = salesSlice.actions;
export default salesSlice.reducer;

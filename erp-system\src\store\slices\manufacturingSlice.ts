import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Recipe, ProductionOrder, PaginatedResponse } from '../../types';

interface ManufacturingState {
  recipes: Recipe[];
  productionOrders: ProductionOrder[];
  loading: boolean;
  error: string | null;
  selectedRecipe: Recipe | null;
  selectedOrder: ProductionOrder | null;
}

const initialState: ManufacturingState = {
  recipes: [],
  productionOrders: [],
  loading: false,
  error: null,
  selectedRecipe: null,
  selectedOrder: null,
};

export const fetchRecipes = createAsyncThunk(
  'manufacturing/fetchRecipes',
  async () => {
    // Mock API call
    const mockRecipes: Recipe[] = [
      {
        id: '1',
        code: 'R001',
        name: 'وصفة المنتج 1',
        productId: '1',
        outputQuantity: 10,
        isActive: true,
        ingredients: [
          {
            id: '1',
            productId: '2',
            quantity: 5,
            unitCost: 10,
          },
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    return mockRecipes;
  }
);

export const fetchProductionOrders = createAsyncThunk(
  'manufacturing/fetchProductionOrders',
  async () => {
    // Mock API call
    const mockOrders: ProductionOrder[] = [
      {
        id: '1',
        orderNumber: 'MO-001',
        recipeId: '1',
        plannedQuantity: 100,
        producedQuantity: 80,
        startDate: new Date(),
        status: 'in_progress',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];
    return mockOrders;
  }
);

const manufacturingSlice = createSlice({
  name: 'manufacturing',
  initialState,
  reducers: {
    setSelectedRecipe: (state, action: PayloadAction<Recipe | null>) => {
      state.selectedRecipe = action.payload;
    },
    setSelectedOrder: (state, action: PayloadAction<ProductionOrder | null>) => {
      state.selectedOrder = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchRecipes.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRecipes.fulfilled, (state, action) => {
        state.loading = false;
        state.recipes = action.payload;
      })
      .addCase(fetchRecipes.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'فشل في تحميل الوصفات';
      })
      .addCase(fetchProductionOrders.fulfilled, (state, action) => {
        state.productionOrders = action.payload;
      });
  },
});

export const { setSelectedRecipe, setSelectedOrder, clearError } = manufacturingSlice.actions;
export default manufacturingSlice.reducer;

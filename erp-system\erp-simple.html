<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام الإدارة المتكامل - ERP</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
        padding: 20px;
      }
      
      .container {
        max-width: 1400px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.1);
        padding: 30px;
        border-radius: 20px;
        backdrop-filter: blur(15px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }
      
      .header {
        text-align: center;
        margin-bottom: 40px;
      }
      
      .header h1 {
        font-size: 3em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      }
      
      .header p {
        font-size: 1.2em;
        opacity: 0.9;
      }
      
      /* Navigation */
      .nav-menu {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin: 30px 0;
        flex-wrap: wrap;
      }
      
      .nav-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        font-weight: bold;
      }
      
      .nav-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }
      
      .nav-btn.active {
        background: rgba(255, 255, 255, 0.4);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
      }
      
      /* Cards */
      .cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin: 40px 0;
      }
      
      .card {
        background: rgba(255, 255, 255, 0.2);
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
        cursor: pointer;
      }
      
      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
      }
      
      .card-icon {
        font-size: 3em;
        margin-bottom: 15px;
      }
      
      .card-title {
        font-size: 1.3em;
        margin-bottom: 10px;
        font-weight: bold;
      }
      
      .card-value {
        font-size: 2.2em;
        font-weight: bold;
        margin: 15px 0;
      }
      
      .card-btn {
        background: rgba(255, 255, 255, 0.3);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        cursor: pointer;
        margin-top: 10px;
        transition: all 0.3s ease;
        font-weight: bold;
      }
      
      .card-btn:hover {
        background: rgba(255, 255, 255, 0.5);
        transform: scale(1.05);
      }
      
      /* Quick Actions */
      .quick-actions {
        margin: 40px 0;
        text-align: center;
      }
      
      .quick-actions h3 {
        font-size: 1.8em;
        margin-bottom: 20px;
      }
      
      .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 20px;
      }
      
      .action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 20px;
        border-radius: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1.1em;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        font-weight: bold;
      }
      
      .action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
      }
      
      .btn-icon {
        font-size: 1.5em;
      }
      
      /* Content Sections */
      .content-section {
        background: rgba(255, 255, 255, 0.1);
        padding: 30px;
        border-radius: 15px;
        margin: 20px 0;
        display: none;
      }
      
      .content-section.active {
        display: block;
      }
      
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
      }
      
      .section-header h2 {
        font-size: 2em;
        color: white;
      }
      
      .add-btn {
        background: #4caf50;
        border: none;
        color: white;
        padding: 12px 24px;
        border-radius: 10px;
        cursor: pointer;
        font-weight: bold;
        font-size: 1em;
        transition: all 0.3s ease;
      }
      
      .add-btn:hover {
        background: #45a049;
        transform: translateY(-2px);
      }
      
      /* Tables */
      .table-container {
        overflow-x: auto;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        margin-top: 20px;
      }
      
      .data-table {
        width: 100%;
        border-collapse: collapse;
        color: white;
      }
      
      .data-table th,
      .data-table td {
        padding: 15px;
        text-align: right;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      }
      
      .data-table th {
        background: rgba(255, 255, 255, 0.2);
        font-weight: bold;
        font-size: 1.1em;
      }
      
      .data-table tr:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      
      .btn-small {
        background: rgba(255, 255, 255, 0.3);
        border: none;
        color: white;
        padding: 8px 12px;
        border-radius: 8px;
        cursor: pointer;
        margin: 0 3px;
        font-size: 0.9em;
        transition: all 0.3s ease;
      }
      
      .btn-small:hover {
        background: rgba(255, 255, 255, 0.5);
        transform: scale(1.05);
      }
      
      .status-paid {
        background: #4caf50;
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.9em;
        font-weight: bold;
      }
      
      .status-pending {
        background: #ff9800;
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.9em;
        font-weight: bold;
      }
      
      /* Status */
      .status {
        background: rgba(76, 175, 80, 0.3);
        border: 2px solid #4caf50;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        margin-top: 30px;
        font-size: 1.2em;
        font-weight: bold;
      }
      
      /* Responsive */
      @media (max-width: 768px) {
        .nav-menu {
          flex-direction: column;
          gap: 8px;
        }
        
        .nav-btn {
          width: 100%;
          text-align: center;
        }
        
        .cards {
          grid-template-columns: 1fr;
        }
        
        .action-buttons {
          grid-template-columns: 1fr;
        }
        
        .section-header {
          flex-direction: column;
          gap: 15px;
          text-align: center;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎉 نظام الإدارة المتكامل</h1>
        <p>نظام ERP شامل لإدارة الأعمال والمؤسسات</p>
      </div>

      <!-- Navigation Menu -->
      <div class="nav-menu">
        <button class="nav-btn active" onclick="showSection('dashboard')">📊 الداشبورد</button>
        <button class="nav-btn" onclick="showSection('sales')">💰 المبيعات</button>
        <button class="nav-btn" onclick="showSection('purchases')">🛒 المشتريات</button>
        <button class="nav-btn" onclick="showSection('inventory')">📦 المخزون</button>
        <button class="nav-btn" onclick="showSection('manufacturing')">🏭 التصنيع</button>
        <button class="nav-btn" onclick="showSection('customers')">👥 العملاء</button>
        <button class="nav-btn" onclick="showSection('suppliers')">🏢 الموردين</button>
        <button class="nav-btn" onclick="showSection('accounting')">💼 المحاسبة</button>
        <button class="nav-btn" onclick="showSection('reports')">📈 التقارير</button>
        <button class="nav-btn" onclick="showSection('settings')">⚙️ الإعدادات</button>
      </div>

      <!-- Dashboard Section -->
      <div id="dashboard" class="content-section active">
        <div class="cards">
          <div class="card" onclick="showSection('sales')">
            <div class="card-icon">📊</div>
            <div class="card-title">المبيعات اليومية</div>
            <div class="card-value" style="color: #4fc3f7;">25,430 ر.س</div>
            <p>إجمالي مبيعات اليوم</p>
            <button class="card-btn" onclick="event.stopPropagation(); showSection('sales')">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showSection('purchases')">
            <div class="card-icon">🛒</div>
            <div class="card-title">المشتريات</div>
            <div class="card-value" style="color: #ff7043;">18,250 ر.س</div>
            <p>إجمالي المشتريات</p>
            <button class="card-btn" onclick="event.stopPropagation(); showSection('purchases')">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showSection('inventory')">
            <div class="card-icon">📦</div>
            <div class="card-title">المخزون</div>
            <div class="card-value" style="color: #66bb6a;">125,800 ر.س</div>
            <p>قيمة المخزون الحالي</p>
            <button class="card-btn" onclick="event.stopPropagation(); showSection('inventory')">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showSection('customers')">
            <div class="card-icon">👥</div>
            <div class="card-title">العملاء</div>
            <div class="card-value" style="color: #ffa726;">1,245</div>
            <p>عدد العملاء المسجلين</p>
            <button class="card-btn" onclick="event.stopPropagation(); showSection('customers')">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showSection('manufacturing')">
            <div class="card-icon">🏭</div>
            <div class="card-title">أوامر الإنتاج</div>
            <div class="card-value" style="color: #9c27b0;">15</div>
            <p>أوامر إنتاج نشطة</p>
            <button class="card-btn" onclick="event.stopPropagation(); showSection('manufacturing')">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showSection('accounting')">
            <div class="card-icon">💼</div>
            <div class="card-title">الرصيد النقدي</div>
            <div class="card-value" style="color: #00bcd4;">245,680 ر.س</div>
            <p>إجمالي الأرصدة</p>
            <button class="card-btn" onclick="event.stopPropagation(); showSection('accounting')">عرض التفاصيل</button>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <h3>🚀 إجراءات سريعة</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="newSale()">
              <span class="btn-icon">💰</span>
              فاتورة مبيعات جديدة
            </button>
            <button class="action-btn" onclick="newPurchase()">
              <span class="btn-icon">🛒</span>
              أمر شراء جديد
            </button>
            <button class="action-btn" onclick="addProduct()">
              <span class="btn-icon">📦</span>
              إضافة منتج
            </button>
            <button class="action-btn" onclick="addCustomer()">
              <span class="btn-icon">👤</span>
              عميل جديد
            </button>
            <button class="action-btn" onclick="newProductionOrder()">
              <span class="btn-icon">🏭</span>
              أمر إنتاج جديد
            </button>
            <button class="action-btn" onclick="generateReport()">
              <span class="btn-icon">📈</span>
              تقرير سريع
            </button>
          </div>
        </div>
      </div>

      <!-- Sales Section -->
      <div id="sales" class="content-section">
        <div class="section-header">
          <h2>💰 إدارة المبيعات</h2>
          <button class="add-btn" onclick="newSale()">+ فاتورة جديدة</button>
        </div>
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>رقم الفاتورة</th>
                <th>العميل</th>
                <th>التاريخ</th>
                <th>المبلغ</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>INV-001</td>
                <td>أحمد محمد</td>
                <td>2024-01-15</td>
                <td>2,500 ر.س</td>
                <td><span class="status-paid">مدفوعة</span></td>
                <td>
                  <button class="btn-small" onclick="viewInvoice('INV-001')">عرض</button>
                  <button class="btn-small" onclick="printInvoice('INV-001')">طباعة</button>
                  <button class="btn-small" onclick="editInvoice('INV-001')">تعديل</button>
                </td>
              </tr>
              <tr>
                <td>INV-002</td>
                <td>فاطمة علي</td>
                <td>2024-01-14</td>
                <td>1,800 ر.س</td>
                <td><span class="status-pending">معلقة</span></td>
                <td>
                  <button class="btn-small" onclick="viewInvoice('INV-002')">عرض</button>
                  <button class="btn-small" onclick="printInvoice('INV-002')">طباعة</button>
                  <button class="btn-small" onclick="editInvoice('INV-002')">تعديل</button>
                </td>
              </tr>
              <tr>
                <td>INV-003</td>
                <td>محمد سالم</td>
                <td>2024-01-13</td>
                <td>3,200 ر.س</td>
                <td><span class="status-paid">مدفوعة</span></td>
                <td>
                  <button class="btn-small" onclick="viewInvoice('INV-003')">عرض</button>
                  <button class="btn-small" onclick="printInvoice('INV-003')">طباعة</button>
                  <button class="btn-small" onclick="editInvoice('INV-003')">تعديل</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Other Sections (Simplified) -->
      <div id="purchases" class="content-section">
        <div class="section-header">
          <h2>🛒 إدارة المشتريات</h2>
          <button class="add-btn" onclick="newPurchase()">+ أمر شراء جديد</button>
        </div>
        <p style="text-align: center; font-size: 1.2em; margin: 40px 0;">
          🚧 قسم المشتريات قيد التطوير - سيتم إضافة المزيد من الوظائف قريباً
        </p>
      </div>

      <div id="inventory" class="content-section">
        <div class="section-header">
          <h2>📦 إدارة المخزون</h2>
          <button class="add-btn" onclick="addProduct()">+ منتج جديد</button>
        </div>
        <p style="text-align: center; font-size: 1.2em; margin: 40px 0;">
          🚧 قسم المخزون قيد التطوير - سيتم إضافة المزيد من الوظائف قريباً
        </p>
      </div>

      <div id="manufacturing" class="content-section">
        <div class="section-header">
          <h2>🏭 إدارة التصنيع</h2>
          <button class="add-btn" onclick="newProductionOrder()">+ أمر إنتاج جديد</button>
        </div>
        <p style="text-align: center; font-size: 1.2em; margin: 40px 0;">
          🚧 قسم التصنيع قيد التطوير - سيتم إضافة المزيد من الوظائف قريباً
        </p>
      </div>

      <div id="customers" class="content-section">
        <div class="section-header">
          <h2>👥 إدارة العملاء</h2>
          <button class="add-btn" onclick="addCustomer()">+ عميل جديد</button>
        </div>
        <p style="text-align: center; font-size: 1.2em; margin: 40px 0;">
          🚧 قسم العملاء قيد التطوير - سيتم إضافة المزيد من الوظائف قريباً
        </p>
      </div>

      <div id="suppliers" class="content-section">
        <div class="section-header">
          <h2>🏢 إدارة الموردين</h2>
          <button class="add-btn" onclick="addSupplier()">+ مورد جديد</button>
        </div>
        <p style="text-align: center; font-size: 1.2em; margin: 40px 0;">
          🚧 قسم الموردين قيد التطوير - سيتم إضافة المزيد من الوظائف قريباً
        </p>
      </div>

      <div id="accounting" class="content-section">
        <div class="section-header">
          <h2>💼 إدارة المحاسبة</h2>
          <button class="add-btn" onclick="newAccount()">+ حساب جديد</button>
        </div>
        <p style="text-align: center; font-size: 1.2em; margin: 40px 0;">
          🚧 قسم المحاسبة قيد التطوير - سيتم إضافة المزيد من الوظائف قريباً
        </p>
      </div>

      <div id="reports" class="content-section">
        <div class="section-header">
          <h2>📈 التقارير والإحصائيات</h2>
          <button class="add-btn" onclick="generateReport()">+ تقرير جديد</button>
        </div>
        <p style="text-align: center; font-size: 1.2em; margin: 40px 0;">
          🚧 قسم التقارير قيد التطوير - سيتم إضافة المزيد من الوظائف قريباً
        </p>
      </div>

      <div id="settings" class="content-section">
        <div class="section-header">
          <h2>⚙️ إعدادات النظام</h2>
          <button class="add-btn" onclick="saveSettings()">💾 حفظ الإعدادات</button>
        </div>
        <p style="text-align: center; font-size: 1.2em; margin: 40px 0;">
          🚧 قسم الإعدادات قيد التطوير - سيتم إضافة المزيد من الوظائف قريباً
        </p>
      </div>

      <!-- Status -->
      <div class="status">
        <strong>✅ نظام الإدارة المتكامل يعمل بنجاح!</strong><br>
        جميع الخدمات متاحة ومتصلة بشكل صحيح
      </div>
    </div>

    <script>
      console.log('🎉 نظام الإدارة المتكامل يعمل بنجاح!');

      // Navigation Function
      function showSection(sectionId) {
        // Hide all sections
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
          section.classList.remove('active');
        });

        // Remove active class from all nav buttons
        const navButtons = document.querySelectorAll('.nav-btn');
        navButtons.forEach(btn => {
          btn.classList.remove('active');
        });

        // Show selected section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
          targetSection.classList.add('active');
        }

        // Add active class to clicked nav button
        const clickedButton = event.target;
        if (clickedButton && clickedButton.classList.contains('nav-btn')) {
          clickedButton.classList.add('active');
        }

        console.log(`عرض قسم: ${sectionId}`);
      }

      // Sales Functions
      function newSale() {
        alert('🎉 إنشاء فاتورة مبيعات جديدة!\n\n✅ سيتم فتح نموذج إنشاء فاتورة جديدة\n📝 يمكنك إضافة المنتجات والعميل\n💰 حساب المجموع تلقائياً');
        console.log('إنشاء فاتورة مبيعات جديدة');
      }

      function viewInvoice(invoiceId) {
        alert(`📄 عرض الفاتورة: ${invoiceId}\n\n✅ تفاصيل الفاتورة\n📋 قائمة المنتجات\n💰 المبلغ الإجمالي\n👤 بيانات العميل`);
        console.log(`عرض الفاتورة: ${invoiceId}`);
      }

      function printInvoice(invoiceId) {
        alert(`🖨️ طباعة الفاتورة: ${invoiceId}\n\n✅ سيتم فتح نافذة الطباعة\n📄 تنسيق احترافي\n🏢 شعار الشركة`);
        console.log(`طباعة الفاتورة: ${invoiceId}`);
      }

      function editInvoice(invoiceId) {
        alert(`✏️ تعديل الفاتورة: ${invoiceId}\n\n✅ تعديل بيانات العميل\n📝 إضافة/حذف منتجات\n💰 تحديث الأسعار\n📅 تغيير التاريخ`);
        console.log(`تعديل الفاتورة: ${invoiceId}`);
      }

      // Other Functions
      function newPurchase() {
        alert('🛒 إنشاء أمر شراء جديد!\n\n✅ سيتم فتح نموذج أمر شراء جديد\n📦 يمكنك إضافة المنتجات والمورد\n💵 تتبع حالة الطلب');
        console.log('إنشاء أمر شراء جديد');
      }

      function addProduct() {
        alert('📦 إضافة منتج جديد!\n\n✅ سيتم فتح نموذج إضافة منتج\n🏷️ إدخال اسم المنتج والسعر\n📊 تحديد الفئة والوحدة');
        console.log('إضافة منتج جديد');
      }

      function addCustomer() {
        alert('👤 إضافة عميل جديد!\n\n✅ سيتم فتح نموذج إضافة عميل\n📞 إدخال بيانات الاتصال\n🏠 العنوان ومعلومات إضافية');
        console.log('إضافة عميل جديد');
      }

      function newProductionOrder() {
        alert('🏭 إنشاء أمر إنتاج جديد!\n\n✅ اختيار المنتج النهائي\n📋 تحديد الكمية المطلوبة\n📅 تحديد تاريخ البدء والانتهاء\n🔧 تخصيص مراكز العمل');
        console.log('إنشاء أمر إنتاج جديد');
      }

      function addSupplier() {
        alert('🏢 إضافة مورد جديد!\n\n✅ معلومات الشركة\n👤 بيانات الشخص المسؤول\n📞 معلومات الاتصال\n💰 شروط الدفع والتوريد');
        console.log('إضافة مورد جديد');
      }

      function newAccount() {
        alert('💼 إنشاء حساب جديد!\n\n✅ رقم الحساب\n📝 اسم الحساب\n📊 نوع الحساب\n💰 الرصيد الافتتاحي');
        console.log('إنشاء حساب جديد');
      }

      function generateReport() {
        alert('📈 إنشاء تقرير جديد!\n\n✅ اختيار نوع التقرير\n📅 تحديد الفترة الزمنية\n📊 تخصيص البيانات\n🖨️ طباعة أو تصدير');
        console.log('إنشاء تقرير جديد');
      }

      function saveSettings() {
        alert('💾 حفظ الإعدادات!\n\n✅ تم حفظ جميع الإعدادات\n🔄 سيتم تطبيق التغييرات فوراً\n🌐 إعدادات النظام محدثة');
        console.log('حفظ الإعدادات');
      }

      // Interactive Effects
      document.addEventListener('DOMContentLoaded', function() {
        // Add hover effects to cards
        document.querySelectorAll('.card').forEach(card => {
          card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
          });
          card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
          });
        });

        // Update time in title
        setInterval(() => {
          const now = new Date();
          const timeStr = now.toLocaleTimeString('ar-SA');
          document.title = `نظام الإدارة المتكامل - ${timeStr}`;
        }, 1000);
      });

      // Welcome message
      setTimeout(() => {
        console.log('مرحباً بك في نظام الإدارة المتكامل! 🚀');
        alert('🎉 مرحباً بك في نظام الإدارة المتكامل!\n\n✅ النظام يعمل بكامل مميزاته\n🖱️ انقر على أي زر للتنقل\n📊 استخدم الداشبورد لمتابعة الإحصائيات\n🚀 جرب الإجراءات السريعة');
      }, 2000);
    </script>
  </body>
</html>

import React, { useEffect } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  Chip,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { fetchRecipes, fetchProductionOrders } from '../../store/slices/manufacturingSlice';

const Manufacturing: React.FC = () => {
  const dispatch = useDispatch();
  const { recipes, productionOrders, loading } = useSelector((state: RootState) => state.manufacturing);
  const [tabValue, setTabValue] = React.useState(0);

  useEffect(() => {
    dispatch(fetchRecipes() as any);
    dispatch(fetchProductionOrders() as any);
  }, [dispatch]);

  const recipeColumns: GridColDef[] = [
    { field: 'code', headerName: 'الكود', width: 120 },
    { field: 'name', headerName: 'اسم الوصفة', width: 200 },
    { field: 'productId', headerName: 'المنتج النهائي', width: 200 },
    { field: 'outputQuantity', headerName: 'كمية الإنتاج', width: 150 },
    {
      field: 'isActive',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      ),
    },
  ];

  const productionColumns: GridColDef[] = [
    { field: 'orderNumber', headerName: 'رقم الأمر', width: 150 },
    { field: 'recipeId', headerName: 'الوصفة', width: 200 },
    { field: 'plannedQuantity', headerName: 'الكمية المخططة', width: 150 },
    { field: 'producedQuantity', headerName: 'الكمية المنتجة', width: 150 },
    {
      field: 'startDate',
      headerName: 'تاريخ البداية',
      width: 150,
      valueFormatter: (params) => new Date(params.value).toLocaleDateString('ar-SA'),
    },
    {
      field: 'status',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params) => {
        const statusColors = {
          planned: 'default',
          in_progress: 'primary',
          completed: 'success',
          cancelled: 'error',
        } as const;
        
        const statusLabels = {
          planned: 'مخطط',
          in_progress: 'قيد التنفيذ',
          completed: 'مكتمل',
          cancelled: 'ملغي',
        } as const;

        return (
          <Chip
            label={statusLabels[params.value as keyof typeof statusLabels]}
            color={statusColors[params.value as keyof typeof statusColors]}
            size="small"
          />
        );
      },
    },
  ];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">إدارة التصنيع</Typography>
        <Button variant="contained" startIcon={<AddIcon />}>
          {tabValue === 0 ? 'وصفة جديدة' : 'أمر إنتاج جديد'}
        </Button>
      </Box>

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="الوصفات" />
            <Tab label="أوامر الإنتاج" />
          </Tabs>
        </Box>

        <CardContent>
          {/* Filters */}
          <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="البحث"
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
          </Grid>

          {/* Tables */}
          {tabValue === 0 ? (
            <DataGrid
              rows={recipes}
              columns={recipeColumns}
              loading={loading}
              autoHeight
              disableSelectionOnClick
            />
          ) : (
            <DataGrid
              rows={productionOrders}
              columns={productionColumns}
              loading={loading}
              autoHeight
              disableSelectionOnClick
            />
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default Manufacturing;

import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  Typography,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import {
  fetchProducts,
  fetchCategories,
  fetchUnits,
  createProduct,
  updateProduct,
  deleteProduct,
  setSelectedProduct,
  setFilters,
} from '../../store/slices/productsSlice';
import { Product } from '../../types';

const Products: React.FC = () => {
  const dispatch = useDispatch();
  const {
    products,
    categories,
    units,
    loading,
    selectedProduct,
    filters,
    pagination,
  } = useSelector((state: RootState) => state.products);

  const [dialogOpen, setDialogOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<Product>>({});

  useEffect(() => {
    dispatch(fetchProducts({}) as any);
    dispatch(fetchCategories() as any);
    dispatch(fetchUnits() as any);
  }, [dispatch]);

  const handleAddProduct = () => {
    setFormData({
      code: '',
      name: '',
      description: '',
      categoryId: '',
      unitId: '',
      price: 0,
      cost: 0,
      minStock: 0,
      maxStock: 0,
      currentStock: 0,
      isActive: true,
    });
    setDialogOpen(true);
  };

  const handleEditProduct = (product: Product) => {
    setFormData(product);
    dispatch(setSelectedProduct(product) as any);
    setDialogOpen(true);
  };

  const handleDeleteProduct = (productId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      dispatch(deleteProduct(productId) as any);
    }
  };

  const handleSaveProduct = () => {
    if (selectedProduct) {
      dispatch(updateProduct(formData as Product) as any);
    } else {
      dispatch(createProduct(formData as Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) as any);
    }
    setDialogOpen(false);
    setFormData({});
    dispatch(setSelectedProduct(null) as any);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setFormData({});
    dispatch(setSelectedProduct(null) as any);
  };

  const columns: GridColDef[] = [
    { field: 'code', headerName: 'الكود', width: 120 },
    { field: 'name', headerName: 'اسم المنتج', width: 200 },
    {
      field: 'categoryId',
      headerName: 'الفئة',
      width: 150,
      valueGetter: (params) => {
        const category = categories.find(c => c.id === params.row.categoryId);
        return category?.name || '';
      },
    },
    {
      field: 'unitId',
      headerName: 'الوحدة',
      width: 100,
      valueGetter: (params) => {
        const unit = units.find(u => u.id === params.row.unitId);
        return unit?.name || '';
      },
    },
    {
      field: 'price',
      headerName: 'السعر',
      width: 120,
      valueFormatter: (params) => `${params.value} ر.س`,
    },
    {
      field: 'currentStock',
      headerName: 'المخزون الحالي',
      width: 130,
    },
    {
      field: 'isActive',
      headerName: 'الحالة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نشط' : 'غير نشط'}
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<EditIcon />}
          label="تعديل"
          onClick={() => handleEditProduct(params.row)}
        />,
        <GridActionsCellItem
          icon={<DeleteIcon />}
          label="حذف"
          onClick={() => handleDeleteProduct(params.row.id)}
        />,
      ],
    },
  ];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">إدارة المنتجات</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddProduct}
        >
          إضافة منتج جديد
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="البحث"
                value={filters.search}
                onChange={(e) => dispatch(setFilters({ search: e.target.value }) as any)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>الفئة</InputLabel>
                <Select
                  value={filters.categoryId}
                  label="الفئة"
                  onChange={(e) => dispatch(setFilters({ categoryId: e.target.value }) as any)}
                >
                  <MenuItem value="">جميع الفئات</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>الحالة</InputLabel>
                <Select
                  value={filters.isActive === null ? '' : filters.isActive.toString()}
                  label="الحالة"
                  onChange={(e) => {
                    const value = e.target.value === '' ? null : e.target.value === 'true';
                    dispatch(setFilters({ isActive: value }) as any);
                  }}
                >
                  <MenuItem value="">جميع الحالات</MenuItem>
                  <MenuItem value="true">نشط</MenuItem>
                  <MenuItem value="false">غير نشط</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <DataGrid
          rows={products}
          columns={columns}
          loading={loading}
          paginationMode="server"
          rowCount={pagination.total}
          page={pagination.page - 1}
          pageSize={pagination.limit}
          onPageChange={(page) => {
            dispatch(fetchProducts({ page: page + 1, limit: pagination.limit }) as any);
          }}
          onPageSizeChange={(pageSize) => {
            dispatch(fetchProducts({ page: 1, limit: pageSize }) as any);
          }}
          autoHeight
          disableSelectionOnClick
        />
      </Card>

      {/* Product Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedProduct ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="كود المنتج"
                value={formData.code || ''}
                onChange={(e) => setFormData({ ...formData, code: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم المنتج"
                value={formData.name || ''}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="وصف المنتج"
                multiline
                rows={3}
                value={formData.description || ''}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>الفئة</InputLabel>
                <Select
                  value={formData.categoryId || ''}
                  label="الفئة"
                  onChange={(e) => setFormData({ ...formData, categoryId: e.target.value })}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>الوحدة</InputLabel>
                <Select
                  value={formData.unitId || ''}
                  label="الوحدة"
                  onChange={(e) => setFormData({ ...formData, unitId: e.target.value })}
                >
                  {units.map((unit) => (
                    <MenuItem key={unit.id} value={unit.id}>
                      {unit.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="السعر"
                type="number"
                value={formData.price || 0}
                onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="التكلفة"
                type="number"
                value={formData.cost || 0}
                onChange={(e) => setFormData({ ...formData, cost: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="الحد الأدنى للمخزون"
                type="number"
                value={formData.minStock || 0}
                onChange={(e) => setFormData({ ...formData, minStock: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="الحد الأقصى للمخزون"
                type="number"
                value={formData.maxStock || 0}
                onChange={(e) => setFormData({ ...formData, maxStock: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="المخزون الحالي"
                type="number"
                value={formData.currentStock || 0}
                onChange={(e) => setFormData({ ...formData, currentStock: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive || false}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                  />
                }
                label="منتج نشط"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          <Button onClick={handleSaveProduct} variant="contained">
            حفظ
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Products;

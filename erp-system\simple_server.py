#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import os
from urllib.parse import urlparse

class ERPHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Cache-Control', 'no-cache')
            self.end_headers()
            
            html_content = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الإدارة المتكامل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 20px;
            overflow-x: hidden;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: fadeIn 1s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        h1 {
            text-align: center;
            font-size: 3.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 10px rgba(255,255,255,0.3); }
            to { text-shadow: 2px 2px 4px rgba(0,0,0,0.5), 0 0 20px rgba(255,255,255,0.6); }
        }
        .subtitle {
            text-align: center;
            font-size: 1.3em;
            margin-bottom: 50px;
            opacity: 0.9;
            animation: slideIn 1s ease-out 0.5s both;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 0.9; transform: translateX(0); }
        }
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 50px 0;
        }
        .card {
            background: rgba(255, 255, 255, 0.15);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            animation: cardSlide 0.6s ease-out;
        }
        @keyframes cardSlide {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .card:nth-child(1) { animation-delay: 0.1s; }
        .card:nth-child(2) { animation-delay: 0.2s; }
        .card:nth-child(3) { animation-delay: 0.3s; }
        .card:nth-child(4) { animation-delay: 0.4s; }
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .card:hover::before {
            left: 100%;
        }
        .card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
            background: rgba(255, 255, 255, 0.2);
        }
        .card-icon {
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .card-title {
            font-size: 1.4em;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .card-value {
            font-size: 2.5em;
            font-weight: bold;
            margin: 20px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        .card-desc {
            opacity: 0.8;
            font-size: 1.1em;
        }
        .status {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4caf50;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin-top: 40px;
            font-size: 1.2em;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
        }
        .loading {
            display: inline-block;
            width: 30px;
            height: 30px;
            border: 4px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin: 0 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .success-message {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 نظام الإدارة المتكامل</h1>
        <p class="subtitle">نظام شامل لإدارة الأعمال والمؤسسات - يعمل بنجاح!</p>
        
        <div class="success-message">
            <strong>✅ تم تشغيل النظام بنجاح!</strong><br>
            الخادم يعمل على المنفذ 9000
        </div>
        
        <div class="cards">
            <div class="card" onclick="showAlert('المبيعات')">
                <div class="card-icon">📊</div>
                <div class="card-title">المبيعات اليومية</div>
                <div class="card-value" style="color: #4fc3f7;">25,430 ر.س</div>
                <div class="card-desc">إجمالي مبيعات اليوم</div>
            </div>
            
            <div class="card" onclick="showAlert('المشتريات')">
                <div class="card-icon">🛒</div>
                <div class="card-title">المشتريات</div>
                <div class="card-value" style="color: #ff7043;">18,250 ر.س</div>
                <div class="card-desc">إجمالي المشتريات</div>
            </div>
            
            <div class="card" onclick="showAlert('المخزون')">
                <div class="card-icon">📦</div>
                <div class="card-title">المخزون</div>
                <div class="card-value" style="color: #66bb6a;">125,800 ر.س</div>
                <div class="card-desc">قيمة المخزون الحالي</div>
            </div>
            
            <div class="card" onclick="showAlert('العملاء')">
                <div class="card-icon">👥</div>
                <div class="card-title">العملاء</div>
                <div class="card-value" style="color: #ffa726;">1,245</div>
                <div class="card-desc">عدد العملاء المسجلين</div>
            </div>
        </div>
        
        <div class="status">
            <strong>🚀 النظام يعمل بنجاح!</strong>
            <div class="loading"></div>
            <br>جميع الخدمات متاحة ومتصلة بشكل صحيح
            <br><small>الوقت الحالي: <span id="current-time"></span></small>
        </div>
    </div>
    
    <script>
        console.log('🎉 نظام الإدارة المتكامل يعمل بنجاح!');
        
        function showAlert(section) {
            alert('مرحباً بك في قسم ' + section + '! 🎉\\n\\nهذا النظام يعمل بنجاح ويمكنك الآن استخدام جميع المميزات.');
        }
        
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('ar-SA');
            const dateStr = now.toLocaleDateString('ar-SA');
            document.getElementById('current-time').textContent = dateStr + ' - ' + timeStr;
            document.title = 'نظام الإدارة المتكامل - ' + timeStr;
        }
        
        updateTime();
        setInterval(updateTime, 1000);
        
        // رسالة ترحيب
        setTimeout(() => {
            alert('🎉 مرحباً بك في نظام الإدارة المتكامل!\\n\\n✅ النظام يعمل بنجاح\\n🚀 جميع المميزات متاحة\\n📊 يمكنك النقر على أي بطاقة للتفاعل\\n\\n🌐 الخادم يعمل على المنفذ 9000');
        }, 2000);
        
        // تأثيرات إضافية
        document.addEventListener('DOMContentLoaded', function() {
            console.log('تم تحميل الصفحة بنجاح! 🎉');
        });
    </script>
</body>
</html>'''
            
            self.wfile.write(html_content.encode('utf-8'))
        else:
            super().do_GET()

if __name__ == "__main__":
    PORT = 9000
    
    print("🚀 بدء تشغيل نظام الإدارة المتكامل...")
    print(f"📡 الخادم يعمل على المنفذ {PORT}")
    print(f"🌐 يمكنك الوصول إلى النظام عبر:")
    print(f"   http://localhost:{PORT}")
    print(f"   http://127.0.0.1:{PORT}")
    print("✅ النظام جاهز للاستخدام!")
    print("=" * 50)
    
    with socketserver.TCPServer(("", PORT), ERPHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف الخادم")
            httpd.shutdown()

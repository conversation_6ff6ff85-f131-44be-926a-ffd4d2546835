import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Supplier, PaginatedResponse } from '../../types';

interface SuppliersState {
  suppliers: Supplier[];
  loading: boolean;
  error: string | null;
  selectedSupplier: Supplier | null;
  filters: {
    search: string;
    isActive: boolean | null;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: SuppliersState = {
  suppliers: [],
  loading: false,
  error: null,
  selectedSupplier: null,
  filters: {
    search: '',
    isActive: null,
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
};

export const fetchSuppliers = createAsyncThunk(
  'suppliers/fetchSuppliers',
  async (params: { page?: number; limit?: number; search?: string }) => {
    // Mock API call
    const mockSuppliers: Supplier[] = [
      {
        id: '1',
        code: 'S001',
        name: 'مورد تجريبي 1',
        email: '<EMAIL>',
        phone: '123456789',
        address: 'العنوان الأول',
        city: 'الرياض',
        country: 'السعودية',
        currentBalance: 15000,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2',
        code: 'S002',
        name: 'مورد تجريبي 2',
        email: '<EMAIL>',
        phone: '987654321',
        address: 'العنوان الثاني',
        city: 'جدة',
        country: 'السعودية',
        currentBalance: 8500,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    return {
      data: mockSuppliers,
      total: mockSuppliers.length,
      page: params.page || 1,
      limit: params.limit || 10,
      totalPages: Math.ceil(mockSuppliers.length / (params.limit || 10)),
    } as PaginatedResponse<Supplier>;
  }
);

export const createSupplier = createAsyncThunk(
  'suppliers/createSupplier',
  async (supplierData: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newSupplier: Supplier = {
      ...supplierData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    return newSupplier;
  }
);

export const updateSupplier = createAsyncThunk(
  'suppliers/updateSupplier',
  async (supplierData: Supplier) => {
    const updatedSupplier: Supplier = {
      ...supplierData,
      updatedAt: new Date(),
    };
    return updatedSupplier;
  }
);

export const deleteSupplier = createAsyncThunk(
  'suppliers/deleteSupplier',
  async (supplierId: string) => {
    return supplierId;
  }
);

const suppliersSlice = createSlice({
  name: 'suppliers',
  initialState,
  reducers: {
    setSelectedSupplier: (state, action: PayloadAction<Supplier | null>) => {
      state.selectedSupplier = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<SuppliersState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action: PayloadAction<Partial<SuppliersState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSuppliers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSuppliers.fulfilled, (state, action) => {
        state.loading = false;
        state.suppliers = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          limit: action.payload.limit,
          total: action.payload.total,
        };
      })
      .addCase(fetchSuppliers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'فشل في تحميل الموردين';
      })
      .addCase(createSupplier.fulfilled, (state, action) => {
        state.suppliers.push(action.payload);
      })
      .addCase(updateSupplier.fulfilled, (state, action) => {
        const index = state.suppliers.findIndex(s => s.id === action.payload.id);
        if (index !== -1) {
          state.suppliers[index] = action.payload;
        }
      })
      .addCase(deleteSupplier.fulfilled, (state, action) => {
        state.suppliers = state.suppliers.filter(s => s.id !== action.payload);
      });
  },
});

export const { setSelectedSupplier, setFilters, setPagination, clearError } = suppliersSlice.actions;
export default suppliersSlice.reducer;

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { PurchaseOrder, PaginatedResponse } from '../../types';

interface PurchasesState {
  orders: PurchaseOrder[];
  loading: boolean;
  error: string | null;
  selectedOrder: PurchaseOrder | null;
  filters: {
    search: string;
    status: string;
    supplierId: string;
    dateFrom: string;
    dateTo: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: PurchasesState = {
  orders: [],
  loading: false,
  error: null,
  selectedOrder: null,
  filters: {
    search: '',
    status: '',
    supplierId: '',
    dateFrom: '',
    dateTo: '',
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
};

export const fetchPurchaseOrders = createAsyncThunk(
  'purchases/fetchPurchaseOrders',
  async (params: any) => {
    // Mock API call
    const mockOrders: PurchaseOrder[] = [
      {
        id: '1',
        orderNumber: 'PO-001',
        supplierId: '1',
        date: new Date(),
        subtotal: 5000,
        taxAmount: 750,
        discountAmount: 250,
        totalAmount: 5500,
        paidAmount: 5500,
        status: 'received',
        items: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    return {
      data: mockOrders,
      total: mockOrders.length,
      page: params.page || 1,
      limit: params.limit || 10,
      totalPages: Math.ceil(mockOrders.length / (params.limit || 10)),
    } as PaginatedResponse<PurchaseOrder>;
  }
);

const purchasesSlice = createSlice({
  name: 'purchases',
  initialState,
  reducers: {
    setSelectedOrder: (state, action: PayloadAction<PurchaseOrder | null>) => {
      state.selectedOrder = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<PurchasesState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action: PayloadAction<Partial<PurchasesState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPurchaseOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPurchaseOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          limit: action.payload.limit,
          total: action.payload.total,
        };
      })
      .addCase(fetchPurchaseOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'فشل في تحميل أوامر الشراء';
      });
  },
});

export const { setSelectedOrder, setFilters, setPagination, clearError } = purchasesSlice.actions;
export default purchasesSlice.reducer;

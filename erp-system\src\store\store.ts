import { configureStore } from '@reduxjs/toolkit';
import productsReducer from './slices/productsSlice';
import customersReducer from './slices/customersSlice';
import suppliersReducer from './slices/suppliersSlice';
import salesReducer from './slices/salesSlice';
import purchasesReducer from './slices/purchasesSlice';
import inventoryReducer from './slices/inventorySlice';
import manufacturingReducer from './slices/manufacturingSlice';

export const store = configureStore({
  reducer: {
    products: productsReducer,
    customers: customersReducer,
    suppliers: suppliersReducer,
    sales: salesReducer,
    purchases: purchasesReducer,
    inventory: inventoryReducer,
    manufacturing: manufacturingReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

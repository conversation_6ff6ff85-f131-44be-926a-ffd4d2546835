<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام الإدارة المتكامل</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.1);
        padding: 30px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }
      h1 {
        text-align: center;
        font-size: 3em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      }
      .subtitle {
        text-align: center;
        font-size: 1.2em;
        margin-bottom: 40px;
        opacity: 0.9;
      }
      .cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin: 40px 0;
      }
      .card {
        background: rgba(255, 255, 255, 0.2);
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
        cursor: pointer;
      }
      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
      }
      .card-icon {
        font-size: 3em;
        margin-bottom: 15px;
      }
      .card-title {
        font-size: 1.3em;
        margin-bottom: 10px;
        font-weight: bold;
      }
      .card-value {
        font-size: 2.2em;
        font-weight: bold;
        margin: 15px 0;
      }
      .status {
        background: rgba(76, 175, 80, 0.3);
        border: 2px solid #4caf50;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        margin-top: 30px;
        font-size: 1.1em;
      }

      /* Navigation Menu */
      .nav-menu {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin: 30px 0;
        flex-wrap: wrap;
      }

      .nav-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        font-weight: bold;
      }

      .nav-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      .nav-btn.active {
        background: rgba(255, 255, 255, 0.4);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
      }

      /* Card Buttons */
      .card-btn {
        background: rgba(255, 255, 255, 0.3);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        cursor: pointer;
        margin-top: 10px;
        transition: all 0.3s ease;
        font-weight: bold;
      }

      .card-btn:hover {
        background: rgba(255, 255, 255, 0.5);
        transform: scale(1.05);
      }

      /* Quick Actions */
      .quick-actions {
        margin: 40px 0;
        text-align: center;
      }

      .quick-actions h3 {
        font-size: 1.5em;
        margin-bottom: 20px;
      }

      .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
      }

      .action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }

      .action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
      }

      .btn-icon {
        font-size: 1.2em;
      }

      /* Content Sections */
      .content-section {
        background: rgba(255, 255, 255, 0.1);
        padding: 20px;
        border-radius: 12px;
        margin: 20px 0;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .add-btn {
        background: #4caf50;
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
      }

      .add-btn:hover {
        background: #45a049;
        transform: translateY(-2px);
      }

      /* Tables */
      .table-container {
        overflow-x: auto;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
      }

      .data-table {
        width: 100%;
        border-collapse: collapse;
        color: white;
      }

      .data-table th,
      .data-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      }

      .data-table th {
        background: rgba(255, 255, 255, 0.2);
        font-weight: bold;
      }

      .data-table tr:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      .btn-small {
        background: rgba(255, 255, 255, 0.3);
        border: none;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        margin: 0 2px;
        font-size: 0.9em;
      }

      .btn-small:hover {
        background: rgba(255, 255, 255, 0.5);
      }

      .status-paid {
        background: #4caf50;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
      }

      .status-pending {
        background: #ff9800;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
      }

      /* Settings Styles */
      .settings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }

      .setting-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .setting-item label {
        font-weight: bold;
        color: white;
      }

      .setting-input {
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 1em;
      }

      .setting-input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.6);
        background: rgba(255, 255, 255, 0.2);
      }

      .setting-checkbox {
        margin-right: 8px;
        transform: scale(1.2);
      }

      /* Responsive Navigation */
      @media (max-width: 768px) {
        .nav-menu {
          flex-direction: column;
          gap: 5px;
        }

        .nav-btn {
          width: 100%;
          text-align: center;
        }

        .cards {
          grid-template-columns: 1fr;
        }

        .action-buttons {
          grid-template-columns: 1fr;
        }

        .settings-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎉 نظام الإدارة المتكامل</h1>
      <p class="subtitle">نظام شامل لإدارة الأعمال والمؤسسات</p>

      <!-- Navigation Menu -->
      <div class="nav-menu">
        <button class="nav-btn active" onclick="showDashboard()">📊 الداشبورد</button>
        <button class="nav-btn" onclick="showSales()">💰 المبيعات</button>
        <button class="nav-btn" onclick="showPurchases()">🛒 المشتريات</button>
        <button class="nav-btn" onclick="showInventory()">📦 المخزون</button>
        <button class="nav-btn" onclick="showManufacturing()">🏭 التصنيع</button>
        <button class="nav-btn" onclick="showCustomers()">👥 العملاء</button>
        <button class="nav-btn" onclick="showSuppliers()">🏢 الموردين</button>
        <button class="nav-btn" onclick="showAccounting()">💼 المحاسبة</button>
        <button class="nav-btn" onclick="showReports()">📈 التقارير</button>
        <button class="nav-btn" onclick="showSettings()">⚙️ الإعدادات</button>
      </div>

      <!-- Dashboard Content -->
      <div id="dashboard-content">
        <div class="cards">
          <div class="card" onclick="showSales()">
            <div class="card-icon">📊</div>
            <div class="card-title">المبيعات اليومية</div>
            <div class="card-value" style="color: #4fc3f7;">25,430 ر.س</div>
            <p>إجمالي مبيعات اليوم</p>
            <button class="card-btn">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showPurchases()">
            <div class="card-icon">🛒</div>
            <div class="card-title">المشتريات</div>
            <div class="card-value" style="color: #ff7043;">18,250 ر.س</div>
            <p>إجمالي المشتريات</p>
            <button class="card-btn">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showInventory()">
            <div class="card-icon">📦</div>
            <div class="card-title">المخزون</div>
            <div class="card-value" style="color: #66bb6a;">125,800 ر.س</div>
            <p>قيمة المخزون الحالي</p>
            <button class="card-btn">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showCustomers()">
            <div class="card-icon">👥</div>
            <div class="card-title">العملاء</div>
            <div class="card-value" style="color: #ffa726;">1,245</div>
            <p>عدد العملاء المسجلين</p>
            <button class="card-btn">عرض التفاصيل</button>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <h3>🚀 إجراءات سريعة</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="newSale()">
              <span class="btn-icon">💰</span>
              فاتورة مبيعات جديدة
            </button>
            <button class="action-btn" onclick="newPurchase()">
              <span class="btn-icon">🛒</span>
              أمر شراء جديد
            </button>
            <button class="action-btn" onclick="addProduct()">
              <span class="btn-icon">📦</span>
              إضافة منتج
            </button>
            <button class="action-btn" onclick="addCustomer()">
              <span class="btn-icon">👤</span>
              عميل جديد
            </button>
          </div>
        </div>
      </div>

      <!-- Sales Content (Hidden) -->
      <div id="sales-content" style="display: none;">
        <h2>💰 إدارة المبيعات</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>فواتير المبيعات</h3>
            <button class="add-btn" onclick="newSale()">+ فاتورة جديدة</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>رقم الفاتورة</th>
                  <th>العميل</th>
                  <th>التاريخ</th>
                  <th>المبلغ</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>INV-001</td>
                  <td>أحمد محمد</td>
                  <td>2024-01-15</td>
                  <td>2,500 ر.س</td>
                  <td><span class="status-paid">مدفوعة</span></td>
                  <td>
                    <button class="btn-small" onclick="viewInvoice('INV-001')">عرض</button>
                    <button class="btn-small" onclick="printInvoice('INV-001')">طباعة</button>
                  </td>
                </tr>
                <tr>
                  <td>INV-002</td>
                  <td>فاطمة علي</td>
                  <td>2024-01-14</td>
                  <td>1,800 ر.س</td>
                  <td><span class="status-pending">معلقة</span></td>
                  <td>
                    <button class="btn-small" onclick="viewInvoice('INV-002')">عرض</button>
                    <button class="btn-small" onclick="printInvoice('INV-002')">طباعة</button>
                  </td>
                </tr>
                <tr>
                  <td>INV-003</td>
                  <td>محمد سالم</td>
                  <td>2024-01-13</td>
                  <td>3,200 ر.س</td>
                  <td><span class="status-paid">مدفوعة</span></td>
                  <td>
                    <button class="btn-small" onclick="viewInvoice('INV-003')">عرض</button>
                    <button class="btn-small" onclick="printInvoice('INV-003')">طباعة</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Purchases Content (Hidden) -->
      <div id="purchases-content" style="display: none;">
        <h2>🛒 إدارة المشتريات</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>أوامر الشراء</h3>
            <button class="add-btn" onclick="newPurchase()">+ أمر شراء جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>رقم الأمر</th>
                  <th>المورد</th>
                  <th>التاريخ</th>
                  <th>المبلغ</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>PO-001</td>
                  <td>شركة التوريدات المتقدمة</td>
                  <td>2024-01-15</td>
                  <td>15,000 ر.س</td>
                  <td><span class="status-pending">قيد التنفيذ</span></td>
                  <td>
                    <button class="btn-small" onclick="viewPurchase('PO-001')">عرض</button>
                    <button class="btn-small" onclick="receivePurchase('PO-001')">استلام</button>
                  </td>
                </tr>
                <tr>
                  <td>PO-002</td>
                  <td>مؤسسة الجودة للتجارة</td>
                  <td>2024-01-12</td>
                  <td>8,500 ر.س</td>
                  <td><span class="status-paid">مكتمل</span></td>
                  <td>
                    <button class="btn-small" onclick="viewPurchase('PO-002')">عرض</button>
                    <button class="btn-small" onclick="printPurchase('PO-002')">طباعة</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Inventory Content (Hidden) -->
      <div id="inventory-content" style="display: none;">
        <h2>📦 إدارة المخزون</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>المنتجات</h3>
            <button class="add-btn" onclick="addProduct()">+ منتج جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>كود المنتج</th>
                  <th>اسم المنتج</th>
                  <th>الفئة</th>
                  <th>الكمية</th>
                  <th>سعر البيع</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>PRD-001</td>
                  <td>لابتوب ديل</td>
                  <td>إلكترونيات</td>
                  <td>25</td>
                  <td>2,500 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="editProduct('PRD-001')">تعديل</button>
                    <button class="btn-small" onclick="adjustStock('PRD-001')">تعديل المخزون</button>
                  </td>
                </tr>
                <tr>
                  <td>PRD-002</td>
                  <td>طابعة HP</td>
                  <td>إلكترونيات</td>
                  <td>15</td>
                  <td>800 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="editProduct('PRD-002')">تعديل</button>
                    <button class="btn-small" onclick="adjustStock('PRD-002')">تعديل المخزون</button>
                  </td>
                </tr>
                <tr>
                  <td>PRD-003</td>
                  <td>كرسي مكتب</td>
                  <td>أثاث</td>
                  <td>8</td>
                  <td>450 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="editProduct('PRD-003')">تعديل</button>
                    <button class="btn-small" onclick="adjustStock('PRD-003')">تعديل المخزون</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Manufacturing Content (Hidden) -->
      <div id="manufacturing-content" style="display: none;">
        <h2>🏭 إدارة التصنيع</h2>

        <!-- Production Orders Section -->
        <div class="content-section">
          <div class="section-header">
            <h3>أوامر الإنتاج</h3>
            <button class="add-btn" onclick="newProductionOrder()">+ أمر إنتاج جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>رقم الأمر</th>
                  <th>المنتج</th>
                  <th>الكمية المطلوبة</th>
                  <th>الكمية المنتجة</th>
                  <th>تاريخ البدء</th>
                  <th>تاريخ الانتهاء المتوقع</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>MO-001</td>
                  <td>لابتوب ديل مجمع</td>
                  <td>50</td>
                  <td>35</td>
                  <td>2024-01-10</td>
                  <td>2024-01-20</td>
                  <td><span class="status-pending">قيد الإنتاج</span></td>
                  <td>
                    <button class="btn-small" onclick="viewProductionOrder('MO-001')">عرض</button>
                    <button class="btn-small" onclick="updateProduction('MO-001')">تحديث</button>
                  </td>
                </tr>
                <tr>
                  <td>MO-002</td>
                  <td>طابعة HP مجمعة</td>
                  <td>30</td>
                  <td>30</td>
                  <td>2024-01-05</td>
                  <td>2024-01-15</td>
                  <td><span class="status-paid">مكتمل</span></td>
                  <td>
                    <button class="btn-small" onclick="viewProductionOrder('MO-002')">عرض</button>
                    <button class="btn-small" onclick="printProductionOrder('MO-002')">طباعة</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Bill of Materials Section -->
        <div class="content-section">
          <div class="section-header">
            <h3>قوائم المواد (BOM)</h3>
            <button class="add-btn" onclick="newBOM()">+ قائمة مواد جديدة</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>كود القائمة</th>
                  <th>المنتج النهائي</th>
                  <th>عدد المكونات</th>
                  <th>التكلفة الإجمالية</th>
                  <th>آخر تحديث</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>BOM-001</td>
                  <td>لابتوب ديل مجمع</td>
                  <td>15</td>
                  <td>2,200 ر.س</td>
                  <td>2024-01-01</td>
                  <td>
                    <button class="btn-small" onclick="viewBOM('BOM-001')">عرض</button>
                    <button class="btn-small" onclick="editBOM('BOM-001')">تعديل</button>
                  </td>
                </tr>
                <tr>
                  <td>BOM-002</td>
                  <td>طابعة HP مجمعة</td>
                  <td>8</td>
                  <td>650 ر.س</td>
                  <td>2024-01-01</td>
                  <td>
                    <button class="btn-small" onclick="viewBOM('BOM-002')">عرض</button>
                    <button class="btn-small" onclick="editBOM('BOM-002')">تعديل</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Work Centers Section -->
        <div class="content-section">
          <div class="section-header">
            <h3>مراكز العمل</h3>
            <button class="add-btn" onclick="newWorkCenter()">+ مركز عمل جديد</button>
          </div>
          <div class="cards">
            <div class="card">
              <div class="card-icon">🔧</div>
              <div class="card-title">مركز التجميع</div>
              <div class="card-value" style="color: #4fc3f7;">نشط</div>
              <p>الطاقة: 80% | العمال: 5</p>
              <button class="card-btn" onclick="viewWorkCenter('WC-001')">عرض التفاصيل</button>
            </div>
            <div class="card">
              <div class="card-icon">⚡</div>
              <div class="card-title">مركز الاختبار</div>
              <div class="card-value" style="color: #66bb6a;">نشط</div>
              <p>الطاقة: 60% | العمال: 3</p>
              <button class="card-btn" onclick="viewWorkCenter('WC-002')">عرض التفاصيل</button>
            </div>
            <div class="card">
              <div class="card-icon">📦</div>
              <div class="card-title">مركز التعبئة</div>
              <div class="card-value" style="color: #ffa726;">نشط</div>
              <p>الطاقة: 90% | العمال: 4</p>
              <button class="card-btn" onclick="viewWorkCenter('WC-003')">عرض التفاصيل</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Customers Content (Hidden) -->
      <div id="customers-content" style="display: none;">
        <h2>👥 إدارة العملاء</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>قائمة العملاء</h3>
            <button class="add-btn" onclick="addCustomer()">+ عميل جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>كود العميل</th>
                  <th>الاسم</th>
                  <th>الهاتف</th>
                  <th>البريد الإلكتروني</th>
                  <th>إجمالي المشتريات</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>CUS-001</td>
                  <td>أحمد محمد</td>
                  <td>0501234567</td>
                  <td><EMAIL></td>
                  <td>15,500 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="viewCustomer('CUS-001')">عرض</button>
                    <button class="btn-small" onclick="editCustomer('CUS-001')">تعديل</button>
                  </td>
                </tr>
                <tr>
                  <td>CUS-002</td>
                  <td>فاطمة علي</td>
                  <td>0507654321</td>
                  <td><EMAIL></td>
                  <td>8,200 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="viewCustomer('CUS-002')">عرض</button>
                    <button class="btn-small" onclick="editCustomer('CUS-002')">تعديل</button>
                  </td>
                </tr>
                <tr>
                  <td>CUS-003</td>
                  <td>محمد سالم</td>
                  <td>0551234567</td>
                  <td><EMAIL></td>
                  <td>12,800 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="viewCustomer('CUS-003')">عرض</button>
                    <button class="btn-small" onclick="editCustomer('CUS-003')">تعديل</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Suppliers Content (Hidden) -->
      <div id="suppliers-content" style="display: none;">
        <h2>🏢 إدارة الموردين</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>قائمة الموردين</h3>
            <button class="add-btn" onclick="addSupplier()">+ مورد جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>كود المورد</th>
                  <th>اسم الشركة</th>
                  <th>الشخص المسؤول</th>
                  <th>الهاتف</th>
                  <th>البريد الإلكتروني</th>
                  <th>إجمالي المشتريات</th>
                  <th>التقييم</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>SUP-001</td>
                  <td>شركة التوريدات المتقدمة</td>
                  <td>أحمد السالم</td>
                  <td>0112345678</td>
                  <td><EMAIL></td>
                  <td>125,000 ر.س</td>
                  <td>⭐⭐⭐⭐⭐</td>
                  <td>
                    <button class="btn-small" onclick="viewSupplier('SUP-001')">عرض</button>
                    <button class="btn-small" onclick="editSupplier('SUP-001')">تعديل</button>
                  </td>
                </tr>
                <tr>
                  <td>SUP-002</td>
                  <td>مؤسسة الجودة للتجارة</td>
                  <td>فاطمة الأحمد</td>
                  <td>0118765432</td>
                  <td><EMAIL></td>
                  <td>89,500 ر.س</td>
                  <td>⭐⭐⭐⭐</td>
                  <td>
                    <button class="btn-small" onclick="viewSupplier('SUP-002')">عرض</button>
                    <button class="btn-small" onclick="editSupplier('SUP-002')">تعديل</button>
                  </td>
                </tr>
                <tr>
                  <td>SUP-003</td>
                  <td>شركة الإلكترونيات الحديثة</td>
                  <td>محمد الخالد</td>
                  <td>0119876543</td>
                  <td><EMAIL></td>
                  <td>156,800 ر.س</td>
                  <td>⭐⭐⭐⭐⭐</td>
                  <td>
                    <button class="btn-small" onclick="viewSupplier('SUP-003')">عرض</button>
                    <button class="btn-small" onclick="editSupplier('SUP-003')">تعديل</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Supplier Performance -->
        <div class="content-section">
          <h3>📊 أداء الموردين</h3>
          <div class="cards">
            <div class="card">
              <div class="card-icon">🏆</div>
              <div class="card-title">أفضل مورد</div>
              <div class="card-value" style="color: #4fc3f7;">شركة الإلكترونيات الحديثة</div>
              <p>تقييم 5 نجوم</p>
            </div>
            <div class="card">
              <div class="card-icon">⏱️</div>
              <div class="card-title">أسرع توريد</div>
              <div class="card-value" style="color: #66bb6a;">3 أيام</div>
              <p>متوسط وقت التسليم</p>
            </div>
            <div class="card">
              <div class="card-icon">💰</div>
              <div class="card-title">أكبر مورد</div>
              <div class="card-value" style="color: #ffa726;">156,800 ر.س</div>
              <p>إجمالي المشتريات</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Accounting Content (Hidden) -->
      <div id="accounting-content" style="display: none;">
        <h2>💼 إدارة المحاسبة</h2>

        <!-- Chart of Accounts -->
        <div class="content-section">
          <div class="section-header">
            <h3>دليل الحسابات</h3>
            <button class="add-btn" onclick="newAccount()">+ حساب جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>رقم الحساب</th>
                  <th>اسم الحساب</th>
                  <th>نوع الحساب</th>
                  <th>الرصيد</th>
                  <th>آخر حركة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>1001</td>
                  <td>النقدية في الصندوق</td>
                  <td>أصول متداولة</td>
                  <td>45,680 ر.س</td>
                  <td>2024-01-15</td>
                  <td>
                    <button class="btn-small" onclick="viewAccount('1001')">عرض</button>
                    <button class="btn-small" onclick="accountStatement('1001')">كشف حساب</button>
                  </td>
                </tr>
                <tr>
                  <td>1002</td>
                  <td>البنك الأهلي</td>
                  <td>أصول متداولة</td>
                  <td>200,000 ر.س</td>
                  <td>2024-01-14</td>
                  <td>
                    <button class="btn-small" onclick="viewAccount('1002')">عرض</button>
                    <button class="btn-small" onclick="accountStatement('1002')">كشف حساب</button>
                  </td>
                </tr>
                <tr>
                  <td>2001</td>
                  <td>حسابات دائنة - موردين</td>
                  <td>خصوم متداولة</td>
                  <td>89,500 ر.س</td>
                  <td>2024-01-13</td>
                  <td>
                    <button class="btn-small" onclick="viewAccount('2001')">عرض</button>
                    <button class="btn-small" onclick="accountStatement('2001')">كشف حساب</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Journal Entries -->
        <div class="content-section">
          <div class="section-header">
            <h3>قيود اليومية</h3>
            <button class="add-btn" onclick="newJournalEntry()">+ قيد جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>رقم القيد</th>
                  <th>التاريخ</th>
                  <th>الوصف</th>
                  <th>المبلغ</th>
                  <th>المرجع</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>JE-001</td>
                  <td>2024-01-15</td>
                  <td>مبيعات نقدية</td>
                  <td>25,430 ر.س</td>
                  <td>INV-001</td>
                  <td>
                    <button class="btn-small" onclick="viewJournalEntry('JE-001')">عرض</button>
                    <button class="btn-small" onclick="editJournalEntry('JE-001')">تعديل</button>
                  </td>
                </tr>
                <tr>
                  <td>JE-002</td>
                  <td>2024-01-14</td>
                  <td>شراء بضاعة</td>
                  <td>18,250 ر.س</td>
                  <td>PO-001</td>
                  <td>
                    <button class="btn-small" onclick="viewJournalEntry('JE-002')">عرض</button>
                    <button class="btn-small" onclick="editJournalEntry('JE-002')">تعديل</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Reports Content (Hidden) -->
      <div id="reports-content" style="display: none;">
        <h2>📈 التقارير والإحصائيات</h2>

        <div class="content-section">
          <h3>📊 تقارير المبيعات</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="salesReport()">
              <span class="btn-icon">💰</span>
              تقرير المبيعات الشهري
            </button>
            <button class="action-btn" onclick="dailySalesReport()">
              <span class="btn-icon">📅</span>
              تقرير المبيعات اليومي
            </button>
          </div>
        </div>

        <div class="content-section">
          <h3>📦 تقارير المخزون</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="inventoryReport()">
              <span class="btn-icon">📋</span>
              تقرير المخزون الحالي
            </button>
            <button class="action-btn" onclick="lowStockReport()">
              <span class="btn-icon">⚠️</span>
              تقرير المخزون المنخفض
            </button>
          </div>
        </div>

        <div class="content-section">
          <h3>💼 التقارير المالية</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="profitReport()">
              <span class="btn-icon">💹</span>
              تقرير الأرباح والخسائر
            </button>
            <button class="action-btn" onclick="cashFlowReport()">
              <span class="btn-icon">💵</span>
              تقرير التدفق النقدي
            </button>
          </div>
        </div>

        <div class="content-section">
          <h3>📊 الإحصائيات السريعة</h3>
          <div class="cards">
            <div class="card">
              <div class="card-icon">📈</div>
              <div class="card-title">نمو المبيعات</div>
              <div class="card-value" style="color: #4fc3f7;">+15.5%</div>
              <p>مقارنة بالشهر الماضي</p>
            </div>
            <div class="card">
              <div class="card-icon">👥</div>
              <div class="card-title">عملاء جدد</div>
              <div class="card-value" style="color: #66bb6a;">+28</div>
              <p>هذا الشهر</p>
            </div>
            <div class="card">
              <div class="card-icon">📦</div>
              <div class="card-title">منتجات نشطة</div>
              <div class="card-value" style="color: #ffa726;">156</div>
              <p>في المخزون</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Content (Hidden) -->
      <div id="settings-content" style="display: none;">
        <h2>⚙️ إعدادات النظام</h2>

        <!-- Company Settings -->
        <div class="content-section">
          <h3>🏢 إعدادات الشركة</h3>
          <div class="settings-grid">
            <div class="setting-item">
              <label>اسم الشركة:</label>
              <input type="text" value="شركة التقنية المتقدمة" class="setting-input">
            </div>
            <div class="setting-item">
              <label>العنوان:</label>
              <input type="text" value="الرياض، المملكة العربية السعودية" class="setting-input">
            </div>
            <div class="setting-item">
              <label>الهاتف:</label>
              <input type="text" value="0112345678" class="setting-input">
            </div>
            <div class="setting-item">
              <label>البريد الإلكتروني:</label>
              <input type="email" value="<EMAIL>" class="setting-input">
            </div>
            <div class="setting-item">
              <label>الرقم الضريبي:</label>
              <input type="text" value="123456789012345" class="setting-input">
            </div>
            <div class="setting-item">
              <label>العملة الافتراضية:</label>
              <select class="setting-input">
                <option value="SAR" selected>ريال سعودي (ر.س)</option>
                <option value="USD">دولار أمريكي ($)</option>
                <option value="EUR">يورو (€)</option>
              </select>
            </div>
          </div>
          <button class="add-btn" onclick="saveCompanySettings()">💾 حفظ الإعدادات</button>
        </div>

        <!-- User Management -->
        <div class="content-section">
          <div class="section-header">
            <h3>👥 إدارة المستخدمين</h3>
            <button class="add-btn" onclick="addUser()">+ مستخدم جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>اسم المستخدم</th>
                  <th>الاسم الكامل</th>
                  <th>البريد الإلكتروني</th>
                  <th>الدور</th>
                  <th>الحالة</th>
                  <th>آخر دخول</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>admin</td>
                  <td>مدير النظام</td>
                  <td><EMAIL></td>
                  <td>مدير عام</td>
                  <td><span class="status-paid">نشط</span></td>
                  <td>2024-01-15 14:30</td>
                  <td>
                    <button class="btn-small" onclick="editUser('admin')">تعديل</button>
                    <button class="btn-small" onclick="resetPassword('admin')">إعادة تعيين كلمة المرور</button>
                  </td>
                </tr>
                <tr>
                  <td>sales_manager</td>
                  <td>أحمد المبيعات</td>
                  <td><EMAIL></td>
                  <td>مدير مبيعات</td>
                  <td><span class="status-paid">نشط</span></td>
                  <td>2024-01-15 13:45</td>
                  <td>
                    <button class="btn-small" onclick="editUser('sales_manager')">تعديل</button>
                    <button class="btn-small" onclick="resetPassword('sales_manager')">إعادة تعيين كلمة المرور</button>
                  </td>
                </tr>
                <tr>
                  <td>warehouse_clerk</td>
                  <td>فاطمة المخازن</td>
                  <td><EMAIL></td>
                  <td>موظف مخازن</td>
                  <td><span class="status-pending">معطل</span></td>
                  <td>2024-01-10 09:15</td>
                  <td>
                    <button class="btn-small" onclick="editUser('warehouse_clerk')">تعديل</button>
                    <button class="btn-small" onclick="activateUser('warehouse_clerk')">تفعيل</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- System Preferences -->
        <div class="content-section">
          <h3>🔧 تفضيلات النظام</h3>
          <div class="settings-grid">
            <div class="setting-item">
              <label>اللغة:</label>
              <select class="setting-input">
                <option value="ar" selected>العربية</option>
                <option value="en">English</option>
              </select>
            </div>
            <div class="setting-item">
              <label>المنطقة الزمنية:</label>
              <select class="setting-input">
                <option value="Asia/Riyadh" selected>الرياض (GMT+3)</option>
                <option value="UTC">UTC</option>
              </select>
            </div>
            <div class="setting-item">
              <label>تنسيق التاريخ:</label>
              <select class="setting-input">
                <option value="dd/mm/yyyy" selected>يوم/شهر/سنة</option>
                <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
              </select>
            </div>
            <div class="setting-item">
              <label>عدد العناصر في الصفحة:</label>
              <select class="setting-input">
                <option value="10">10</option>
                <option value="25" selected>25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
            </div>
            <div class="setting-item">
              <label>النسخ الاحتياطي التلقائي:</label>
              <select class="setting-input">
                <option value="daily" selected>يومي</option>
                <option value="weekly">أسبوعي</option>
                <option value="monthly">شهري</option>
                <option value="disabled">معطل</option>
              </select>
            </div>
            <div class="setting-item">
              <label>إشعارات البريد الإلكتروني:</label>
              <input type="checkbox" checked class="setting-checkbox">
              <span>تفعيل الإشعارات</span>
            </div>
          </div>
          <button class="add-btn" onclick="saveSystemSettings()">💾 حفظ التفضيلات</button>
        </div>

        <!-- Backup & Security -->
        <div class="content-section">
          <h3>🔒 النسخ الاحتياطي والأمان</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="createBackup()">
              <span class="btn-icon">💾</span>
              إنشاء نسخة احتياطية
            </button>
            <button class="action-btn" onclick="restoreBackup()">
              <span class="btn-icon">📥</span>
              استعادة نسخة احتياطية
            </button>
            <button class="action-btn" onclick="exportData()">
              <span class="btn-icon">📤</span>
              تصدير البيانات
            </button>
            <button class="action-btn" onclick="importData()">
              <span class="btn-icon">📥</span>
              استيراد البيانات
            </button>
          </div>
        </div>
      </div>

      <div class="status">
        <strong>✅ النظام يعمل بنجاح!</strong><br>
        جميع الخدمات متاحة ومتصلة بشكل صحيح
      </div>
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <script>
      console.log('🎉 نظام الإدارة المتكامل يعمل بنجاح!');

      // Navigation Functions
      function showDashboard() {
        hideAllContent();
        document.getElementById('dashboard-content').style.display = 'block';
        setActiveNav('dashboard');
        console.log('عرض الداشبورد');
      }

      function showSales() {
        hideAllContent();
        document.getElementById('sales-content').style.display = 'block';
        setActiveNav('sales');
        console.log('عرض صفحة المبيعات');
        alert('مرحباً بك في قسم المبيعات! 💰\n\nهنا يمكنك إدارة جميع فواتير المبيعات والعملاء.');
      }

      function showPurchases() {
        hideAllContent();
        document.getElementById('purchases-content').style.display = 'block';
        setActiveNav('purchases');
        console.log('عرض صفحة المشتريات');
      }

      function showInventory() {
        hideAllContent();
        document.getElementById('inventory-content').style.display = 'block';
        setActiveNav('inventory');
        console.log('عرض صفحة المخزون');
      }

      function showCustomers() {
        hideAllContent();
        document.getElementById('customers-content').style.display = 'block';
        setActiveNav('customers');
        console.log('عرض صفحة العملاء');
      }

      function showReports() {
        hideAllContent();
        document.getElementById('reports-content').style.display = 'block';
        setActiveNav('reports');
        console.log('عرض صفحة التقارير');
      }

      function showManufacturing() {
        hideAllContent();
        document.getElementById('manufacturing-content').style.display = 'block';
        setActiveNav('manufacturing');
        console.log('عرض صفحة التصنيع');
      }

      function showSuppliers() {
        hideAllContent();
        document.getElementById('suppliers-content').style.display = 'block';
        setActiveNav('suppliers');
        console.log('عرض صفحة الموردين');
      }

      function showAccounting() {
        hideAllContent();
        document.getElementById('accounting-content').style.display = 'block';
        setActiveNav('accounting');
        console.log('عرض صفحة المحاسبة');
      }

      function showSettings() {
        hideAllContent();
        document.getElementById('settings-content').style.display = 'block';
        setActiveNav('settings');
        console.log('عرض صفحة الإعدادات');
      }

      function hideAllContent() {
        const contents = ['dashboard-content', 'sales-content', 'purchases-content', 'inventory-content', 'manufacturing-content', 'customers-content', 'suppliers-content', 'accounting-content', 'reports-content', 'settings-content'];
        contents.forEach(id => {
          const element = document.getElementById(id);
          if (element) element.style.display = 'none';
        });
      }

      function setActiveNav(section) {
        document.querySelectorAll('.nav-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        // Set active based on section if needed
      }

      // Quick Action Functions
      function newSale() {
        alert('🎉 إنشاء فاتورة مبيعات جديدة!\n\n✅ سيتم فتح نموذج إنشاء فاتورة جديدة\n📝 يمكنك إضافة المنتجات والعميل\n💰 حساب المجموع تلقائياً');
        console.log('إنشاء فاتورة مبيعات جديدة');
      }

      function newPurchase() {
        alert('🛒 إنشاء أمر شراء جديد!\n\n✅ سيتم فتح نموذج أمر شراء جديد\n📦 يمكنك إضافة المنتجات والمورد\n💵 تتبع حالة الطلب');
        console.log('إنشاء أمر شراء جديد');
      }

      function addProduct() {
        alert('📦 إضافة منتج جديد!\n\n✅ سيتم فتح نموذج إضافة منتج\n🏷️ إدخال اسم المنتج والسعر\n📊 تحديد الفئة والوحدة');
        console.log('إضافة منتج جديد');
      }

      function addCustomer() {
        alert('👤 إضافة عميل جديد!\n\n✅ سيتم فتح نموذج إضافة عميل\n📞 إدخال بيانات الاتصال\n🏠 العنوان ومعلومات إضافية');
        console.log('إضافة عميل جديد');
      }

      // Sales Functions
      function viewInvoice(invoiceId) {
        alert(`📄 عرض الفاتورة: ${invoiceId}\n\n✅ تفاصيل الفاتورة\n📋 قائمة المنتجات\n💰 المبلغ الإجمالي\n👤 بيانات العميل`);
        console.log(`عرض الفاتورة: ${invoiceId}`);
      }

      function printInvoice(invoiceId) {
        alert(`🖨️ طباعة الفاتورة: ${invoiceId}\n\n✅ سيتم فتح نافذة الطباعة\n📄 تنسيق احترافي\n🏢 شعار الشركة`);
        console.log(`طباعة الفاتورة: ${invoiceId}`);
      }

      // Purchase Functions
      function viewPurchase(purchaseId) {
        alert(`📋 عرض أمر الشراء: ${purchaseId}\n\n✅ تفاصيل الأمر\n📦 قائمة المنتجات\n🏢 بيانات المورد\n📅 تاريخ التسليم المتوقع`);
        console.log(`عرض أمر الشراء: ${purchaseId}`);
      }

      function receivePurchase(purchaseId) {
        alert(`📦 استلام أمر الشراء: ${purchaseId}\n\n✅ تأكيد الاستلام\n📊 تحديث المخزون\n✔️ تغيير حالة الأمر إلى مكتمل`);
        console.log(`استلام أمر الشراء: ${purchaseId}`);
      }

      function printPurchase(purchaseId) {
        alert(`🖨️ طباعة أمر الشراء: ${purchaseId}\n\n✅ سيتم فتح نافذة الطباعة\n📄 تنسيق رسمي\n📋 جميع التفاصيل`);
        console.log(`طباعة أمر الشراء: ${purchaseId}`);
      }

      // Inventory Functions
      function editProduct(productId) {
        alert(`✏️ تعديل المنتج: ${productId}\n\n✅ تعديل اسم المنتج\n💰 تحديث الأسعار\n📂 تغيير الفئة\n📝 إضافة وصف`);
        console.log(`تعديل المنتج: ${productId}`);
      }

      function adjustStock(productId) {
        alert(`📊 تعديل مخزون المنتج: ${productId}\n\n✅ إضافة كمية\n➖ خصم كمية\n📝 سبب التعديل\n📅 تاريخ التعديل`);
        console.log(`تعديل مخزون المنتج: ${productId}`);
      }

      // Customer Functions
      function viewCustomer(customerId) {
        alert(`👤 عرض بيانات العميل: ${customerId}\n\n✅ المعلومات الشخصية\n📞 بيانات الاتصال\n💰 تاريخ المشتريات\n📊 إحصائيات العميل`);
        console.log(`عرض بيانات العميل: ${customerId}`);
      }

      function editCustomer(customerId) {
        alert(`✏️ تعديل بيانات العميل: ${customerId}\n\n✅ تحديث المعلومات\n📞 تعديل الهاتف\n📧 تحديث البريد الإلكتروني\n🏠 تعديل العنوان`);
        console.log(`تعديل بيانات العميل: ${customerId}`);
      }

      // Reports Functions
      function salesReport() {
        alert('📊 تقرير المبيعات الشهري\n\n✅ إجمالي المبيعات: 125,430 ر.س\n📈 نمو 15.5% عن الشهر الماضي\n🏆 أفضل منتج: لابتوب ديل\n📅 الفترة: يناير 2024');
        console.log('عرض تقرير المبيعات الشهري');
      }

      function dailySalesReport() {
        alert('📅 تقرير المبيعات اليومي\n\n✅ مبيعات اليوم: 25,430 ر.س\n📋 عدد الفواتير: 12\n👥 عدد العملاء: 8\n⭐ متوسط قيمة الفاتورة: 2,119 ر.س');
        console.log('عرض تقرير المبيعات اليومي');
      }

      function inventoryReport() {
        alert('📋 تقرير المخزون الحالي\n\n✅ إجمالي قيمة المخزون: 125,800 ر.س\n📦 عدد المنتجات: 156\n⚠️ منتجات منخفضة المخزون: 5\n📈 أسرع المنتجات مبيعاً: لابتوب ديل');
        console.log('عرض تقرير المخزون الحالي');
      }

      function lowStockReport() {
        alert('⚠️ تقرير المخزون المنخفض\n\n🔴 منتجات تحتاج إعادة طلب:\n• كرسي مكتب (8 قطع)\n• ماوس لاسلكي (3 قطع)\n• لوحة مفاتيح (6 قطع)\n• سماعات (4 قطع)\n• كابل USB (2 قطع)');
        console.log('عرض تقرير المخزون المنخفض');
      }

      function profitReport() {
        alert('💹 تقرير الأرباح والخسائر\n\n✅ إجمالي الإيرادات: 125,430 ر.س\n💰 إجمالي التكاليف: 89,500 ر.س\n🎯 صافي الربح: 35,930 ر.س\n📊 هامش الربح: 28.6%');
        console.log('عرض تقرير الأرباح والخسائر');
      }

      function cashFlowReport() {
        alert('💵 تقرير التدفق النقدي\n\n✅ النقد الداخل: 125,430 ر.س\n💸 النقد الخارج: 89,500 ر.س\n💰 صافي التدفق النقدي: +35,930 ر.س\n🏦 الرصيد النقدي: 245,680 ر.س');
        console.log('عرض تقرير التدفق النقدي');
      }

      // Manufacturing Functions
      function newProductionOrder() {
        alert('🏭 إنشاء أمر إنتاج جديد!\n\n✅ اختيار المنتج النهائي\n📋 تحديد الكمية المطلوبة\n📅 تحديد تاريخ البدء والانتهاء\n🔧 تخصيص مراكز العمل');
        console.log('إنشاء أمر إنتاج جديد');
      }

      function viewProductionOrder(orderId) {
        alert(`🏭 عرض أمر الإنتاج: ${orderId}\n\n✅ تفاصيل الأمر\n📊 نسبة الإنجاز\n🔧 مراكز العمل المخصصة\n📋 قائمة المواد المطلوبة\n👷 العمال المخصصين`);
        console.log(`عرض أمر الإنتاج: ${orderId}`);
      }

      function updateProduction(orderId) {
        alert(`📊 تحديث الإنتاج: ${orderId}\n\n✅ تسجيل الكمية المنتجة\n⏱️ تحديث الوقت المستغرق\n🔧 تقرير حالة المعدات\n📝 ملاحظات الإنتاج`);
        console.log(`تحديث الإنتاج: ${orderId}`);
      }

      function printProductionOrder(orderId) {
        alert(`🖨️ طباعة أمر الإنتاج: ${orderId}\n\n✅ تفاصيل الأمر\n📋 قائمة المواد\n🔧 تعليمات التصنيع\n📊 مؤشرات الجودة`);
        console.log(`طباعة أمر الإنتاج: ${orderId}`);
      }

      function newBOM() {
        alert('📋 إنشاء قائمة مواد جديدة!\n\n✅ اختيار المنتج النهائي\n🔧 إضافة المكونات والمواد\n📊 تحديد الكميات المطلوبة\n💰 حساب التكلفة الإجمالية');
        console.log('إنشاء قائمة مواد جديدة');
      }

      function viewBOM(bomId) {
        alert(`📋 عرض قائمة المواد: ${bomId}\n\n✅ المنتج النهائي\n🔧 قائمة المكونات\n📊 الكميات المطلوبة\n💰 تكلفة كل مكون\n📈 إجمالي التكلفة`);
        console.log(`عرض قائمة المواد: ${bomId}`);
      }

      function editBOM(bomId) {
        alert(`✏️ تعديل قائمة المواد: ${bomId}\n\n✅ إضافة/حذف مكونات\n📊 تعديل الكميات\n💰 تحديث الأسعار\n🔄 إعادة حساب التكلفة`);
        console.log(`تعديل قائمة المواد: ${bomId}`);
      }

      function newWorkCenter() {
        alert('🔧 إنشاء مركز عمل جديد!\n\n✅ تحديد اسم المركز\n⚡ تحديد الطاقة الإنتاجية\n👷 تخصيص العمال\n🛠️ تحديد المعدات المتاحة');
        console.log('إنشاء مركز عمل جديد');
      }

      function viewWorkCenter(centerId) {
        alert(`🔧 عرض مركز العمل: ${centerId}\n\n✅ تفاصيل المركز\n📊 الطاقة الحالية\n👷 قائمة العمال\n🛠️ المعدات المتاحة\n📈 إحصائيات الأداء`);
        console.log(`عرض مركز العمل: ${centerId}`);
      }

      // Supplier Functions
      function addSupplier() {
        alert('🏢 إضافة مورد جديد!\n\n✅ معلومات الشركة\n👤 بيانات الشخص المسؤول\n📞 معلومات الاتصال\n💰 شروط الدفع والتوريد');
        console.log('إضافة مورد جديد');
      }

      function viewSupplier(supplierId) {
        alert(`🏢 عرض بيانات المورد: ${supplierId}\n\n✅ معلومات الشركة\n👤 الشخص المسؤول\n📞 بيانات الاتصال\n💰 تاريخ المشتريات\n⭐ تقييم الأداء`);
        console.log(`عرض بيانات المورد: ${supplierId}`);
      }

      function editSupplier(supplierId) {
        alert(`✏️ تعديل بيانات المورد: ${supplierId}\n\n✅ تحديث معلومات الشركة\n👤 تعديل بيانات المسؤول\n📞 تحديث معلومات الاتصال\n⭐ تحديث التقييم`);
        console.log(`تعديل بيانات المورد: ${supplierId}`);
      }

      // Accounting Functions
      function newAccount() {
        alert('💼 إنشاء حساب جديد!\n\n✅ رقم الحساب\n📝 اسم الحساب\n📊 نوع الحساب (أصول/خصوم/حقوق ملكية/إيرادات/مصروفات)\n💰 الرصيد الافتتاحي');
        console.log('إنشاء حساب جديد');
      }

      function viewAccount(accountId) {
        alert(`💼 عرض الحساب: ${accountId}\n\n✅ تفاصيل الحساب\n💰 الرصيد الحالي\n📊 نوع الحساب\n📈 حركات الحساب الأخيرة\n📅 تاريخ آخر حركة`);
        console.log(`عرض الحساب: ${accountId}`);
      }

      function accountStatement(accountId) {
        alert(`📋 كشف حساب: ${accountId}\n\n✅ جميع الحركات\n📅 فترة زمنية محددة\n💰 الأرصدة الجارية\n📊 ملخص الحساب\n🖨️ إمكانية الطباعة`);
        console.log(`كشف حساب: ${accountId}`);
      }

      function newJournalEntry() {
        alert('📝 إنشاء قيد يومية جديد!\n\n✅ تاريخ القيد\n📝 وصف القيد\n💰 المبالغ المدينة والدائنة\n📋 الحسابات المتأثرة\n📎 المرفقات');
        console.log('إنشاء قيد يومية جديد');
      }

      function viewJournalEntry(entryId) {
        alert(`📝 عرض القيد: ${entryId}\n\n✅ تفاصيل القيد\n💰 المبالغ المدينة والدائنة\n📋 الحسابات المتأثرة\n📅 التاريخ والوقت\n📎 المرفقات`);
        console.log(`عرض القيد: ${entryId}`);
      }

      function editJournalEntry(entryId) {
        alert(`✏️ تعديل القيد: ${entryId}\n\n✅ تعديل الوصف\n💰 تعديل المبالغ\n📋 تغيير الحسابات\n📅 تحديث التاريخ\n📎 إضافة/حذف مرفقات`);
        console.log(`تعديل القيد: ${entryId}`);
      }

      // Settings Functions
      function saveCompanySettings() {
        alert('💾 حفظ إعدادات الشركة!\n\n✅ تم حفظ جميع البيانات\n🏢 معلومات الشركة محدثة\n📄 سيتم تطبيق التغييرات على جميع التقارير\n🔄 إعادة تشغيل النظام مطلوبة');
        console.log('حفظ إعدادات الشركة');
      }

      function addUser() {
        alert('👤 إضافة مستخدم جديد!\n\n✅ اسم المستخدم وكلمة المرور\n📧 البريد الإلكتروني\n🔐 تحديد الصلاحيات والأدوار\n📱 معلومات الاتصال');
        console.log('إضافة مستخدم جديد');
      }

      function editUser(userId) {
        alert(`✏️ تعديل المستخدم: ${userId}\n\n✅ تحديث البيانات الشخصية\n🔐 تعديل الصلاحيات\n📧 تغيير البريد الإلكتروني\n📱 تحديث معلومات الاتصال`);
        console.log(`تعديل المستخدم: ${userId}`);
      }

      function resetPassword(userId) {
        alert(`🔑 إعادة تعيين كلمة المرور: ${userId}\n\n✅ سيتم إرسال كلمة مرور مؤقتة\n📧 عبر البريد الإلكتروني\n🔐 المستخدم مطالب بتغييرها عند الدخول\n⏰ صالحة لمدة 24 ساعة`);
        console.log(`إعادة تعيين كلمة المرور: ${userId}`);
      }

      function activateUser(userId) {
        alert(`✅ تفعيل المستخدم: ${userId}\n\n✅ تم تفعيل الحساب بنجاح\n🔓 يمكن للمستخدم الدخول الآن\n📧 سيتم إرسال إشعار بالتفعيل\n📊 سيظهر في قائمة المستخدمين النشطين`);
        console.log(`تفعيل المستخدم: ${userId}`);
      }

      function saveSystemSettings() {
        alert('⚙️ حفظ تفضيلات النظام!\n\n✅ تم حفظ جميع الإعدادات\n🔄 سيتم تطبيق التغييرات فوراً\n🌐 إعدادات اللغة والمنطقة الزمنية محدثة\n📊 تفضيلات العرض محفوظة');
        console.log('حفظ تفضيلات النظام');
      }

      function createBackup() {
        alert('💾 إنشاء نسخة احتياطية!\n\n✅ بدء عملية النسخ الاحتياطي\n📊 نسخ جميع البيانات والإعدادات\n🗜️ ضغط الملفات\n📁 حفظ في مجلد النسخ الاحتياطية\n⏱️ الوقت المتوقع: 5-10 دقائق');
        console.log('إنشاء نسخة احتياطية');
      }

      function restoreBackup() {
        alert('📥 استعادة نسخة احتياطية!\n\n⚠️ تحذير: سيتم استبدال البيانات الحالية\n📁 اختيار ملف النسخة الاحتياطية\n🔄 استعادة جميع البيانات\n⏱️ الوقت المتوقع: 10-15 دقيقة\n🔒 يُنصح بإنشاء نسخة احتياطية قبل الاستعادة');
        console.log('استعادة نسخة احتياطية');
      }

      function exportData() {
        alert('📤 تصدير البيانات!\n\n✅ اختيار نوع البيانات للتصدير\n📊 تنسيقات متاحة: Excel, CSV, PDF\n📁 تحديد مجلد الحفظ\n⏱️ الوقت المتوقع: 2-5 دقائق');
        console.log('تصدير البيانات');
      }

      function importData() {
        alert('📥 استيراد البيانات!\n\n✅ اختيار ملف البيانات\n📊 تنسيقات مدعومة: Excel, CSV\n🔍 فحص البيانات قبل الاستيراد\n⚠️ التحقق من عدم وجود تضارب\n⏱️ الوقت المتوقع: 3-8 دقائق');
        console.log('استيراد البيانات');
      }

      // إضافة تأثيرات تفاعلية
      document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.card').forEach(card => {
          card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
          });
          card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
          });
        });
      });

      // تحديث الوقت
      setInterval(() => {
        const now = new Date();
        const timeStr = now.toLocaleTimeString('ar-SA');
        document.title = `نظام الإدارة المتكامل - ${timeStr}`;
      }, 1000);

      // رسالة ترحيب
      setTimeout(() => {
        console.log('مرحباً بك في نظام الإدارة المتكامل! 🚀');
        alert('🎉 مرحباً بك في نظام الإدارة المتكامل!\n\n✅ النظام يعمل بكامل مميزاته\n🖱️ انقر على أي زر للتنقل\n📊 استخدم الداشبورد لمتابعة الإحصائيات\n🚀 جرب الإجراءات السريعة');
      }, 2000);
    </script>
  </body>
</html>

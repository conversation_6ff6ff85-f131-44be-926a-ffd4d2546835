<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام الإدارة المتكامل</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.1);
        padding: 30px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }
      h1 {
        text-align: center;
        font-size: 3em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      }
      .subtitle {
        text-align: center;
        font-size: 1.2em;
        margin-bottom: 40px;
        opacity: 0.9;
      }
      .cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin: 40px 0;
      }
      .card {
        background: rgba(255, 255, 255, 0.2);
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
        cursor: pointer;
      }
      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
      }
      .card-icon {
        font-size: 3em;
        margin-bottom: 15px;
      }
      .card-title {
        font-size: 1.3em;
        margin-bottom: 10px;
        font-weight: bold;
      }
      .card-value {
        font-size: 2.2em;
        font-weight: bold;
        margin: 15px 0;
      }
      .status {
        background: rgba(76, 175, 80, 0.3);
        border: 2px solid #4caf50;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        margin-top: 30px;
        font-size: 1.1em;
      }

      /* Navigation Menu */
      .nav-menu {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin: 30px 0;
        flex-wrap: wrap;
      }

      .nav-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        font-weight: bold;
      }

      .nav-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      .nav-btn.active {
        background: rgba(255, 255, 255, 0.4);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
      }

      /* Card Buttons */
      .card-btn {
        background: rgba(255, 255, 255, 0.3);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        cursor: pointer;
        margin-top: 10px;
        transition: all 0.3s ease;
        font-weight: bold;
      }

      .card-btn:hover {
        background: rgba(255, 255, 255, 0.5);
        transform: scale(1.05);
      }

      /* Quick Actions */
      .quick-actions {
        margin: 40px 0;
        text-align: center;
      }

      .quick-actions h3 {
        font-size: 1.5em;
        margin-bottom: 20px;
      }

      .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
      }

      .action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1em;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }

      .action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
      }

      .btn-icon {
        font-size: 1.2em;
      }

      /* Content Sections */
      .content-section {
        background: rgba(255, 255, 255, 0.1);
        padding: 20px;
        border-radius: 12px;
        margin: 20px 0;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .add-btn {
        background: #4caf50;
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: bold;
        transition: all 0.3s ease;
      }

      .add-btn:hover {
        background: #45a049;
        transform: translateY(-2px);
      }

      /* Tables */
      .table-container {
        overflow-x: auto;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
      }

      .data-table {
        width: 100%;
        border-collapse: collapse;
        color: white;
      }

      .data-table th,
      .data-table td {
        padding: 12px;
        text-align: right;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      }

      .data-table th {
        background: rgba(255, 255, 255, 0.2);
        font-weight: bold;
      }

      .data-table tr:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      .btn-small {
        background: rgba(255, 255, 255, 0.3);
        border: none;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        margin: 0 2px;
        font-size: 0.9em;
      }

      .btn-small:hover {
        background: rgba(255, 255, 255, 0.5);
      }

      .status-paid {
        background: #4caf50;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
      }

      .status-pending {
        background: #ff9800;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎉 نظام الإدارة المتكامل</h1>
      <p class="subtitle">نظام شامل لإدارة الأعمال والمؤسسات</p>

      <!-- Navigation Menu -->
      <div class="nav-menu">
        <button class="nav-btn active" onclick="showDashboard()">📊 الداشبورد</button>
        <button class="nav-btn" onclick="showSales()">💰 المبيعات</button>
        <button class="nav-btn" onclick="showPurchases()">🛒 المشتريات</button>
        <button class="nav-btn" onclick="showInventory()">📦 المخزون</button>
        <button class="nav-btn" onclick="showCustomers()">👥 العملاء</button>
        <button class="nav-btn" onclick="showReports()">📈 التقارير</button>
      </div>

      <!-- Dashboard Content -->
      <div id="dashboard-content">
        <div class="cards">
          <div class="card" onclick="showSales()">
            <div class="card-icon">📊</div>
            <div class="card-title">المبيعات اليومية</div>
            <div class="card-value" style="color: #4fc3f7;">25,430 ر.س</div>
            <p>إجمالي مبيعات اليوم</p>
            <button class="card-btn">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showPurchases()">
            <div class="card-icon">🛒</div>
            <div class="card-title">المشتريات</div>
            <div class="card-value" style="color: #ff7043;">18,250 ر.س</div>
            <p>إجمالي المشتريات</p>
            <button class="card-btn">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showInventory()">
            <div class="card-icon">📦</div>
            <div class="card-title">المخزون</div>
            <div class="card-value" style="color: #66bb6a;">125,800 ر.س</div>
            <p>قيمة المخزون الحالي</p>
            <button class="card-btn">عرض التفاصيل</button>
          </div>

          <div class="card" onclick="showCustomers()">
            <div class="card-icon">👥</div>
            <div class="card-title">العملاء</div>
            <div class="card-value" style="color: #ffa726;">1,245</div>
            <p>عدد العملاء المسجلين</p>
            <button class="card-btn">عرض التفاصيل</button>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <h3>🚀 إجراءات سريعة</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="newSale()">
              <span class="btn-icon">💰</span>
              فاتورة مبيعات جديدة
            </button>
            <button class="action-btn" onclick="newPurchase()">
              <span class="btn-icon">🛒</span>
              أمر شراء جديد
            </button>
            <button class="action-btn" onclick="addProduct()">
              <span class="btn-icon">📦</span>
              إضافة منتج
            </button>
            <button class="action-btn" onclick="addCustomer()">
              <span class="btn-icon">👤</span>
              عميل جديد
            </button>
          </div>
        </div>
      </div>

      <!-- Sales Content (Hidden) -->
      <div id="sales-content" style="display: none;">
        <h2>💰 إدارة المبيعات</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>فواتير المبيعات</h3>
            <button class="add-btn" onclick="newSale()">+ فاتورة جديدة</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>رقم الفاتورة</th>
                  <th>العميل</th>
                  <th>التاريخ</th>
                  <th>المبلغ</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>INV-001</td>
                  <td>أحمد محمد</td>
                  <td>2024-01-15</td>
                  <td>2,500 ر.س</td>
                  <td><span class="status-paid">مدفوعة</span></td>
                  <td>
                    <button class="btn-small" onclick="viewInvoice('INV-001')">عرض</button>
                    <button class="btn-small" onclick="printInvoice('INV-001')">طباعة</button>
                  </td>
                </tr>
                <tr>
                  <td>INV-002</td>
                  <td>فاطمة علي</td>
                  <td>2024-01-14</td>
                  <td>1,800 ر.س</td>
                  <td><span class="status-pending">معلقة</span></td>
                  <td>
                    <button class="btn-small" onclick="viewInvoice('INV-002')">عرض</button>
                    <button class="btn-small" onclick="printInvoice('INV-002')">طباعة</button>
                  </td>
                </tr>
                <tr>
                  <td>INV-003</td>
                  <td>محمد سالم</td>
                  <td>2024-01-13</td>
                  <td>3,200 ر.س</td>
                  <td><span class="status-paid">مدفوعة</span></td>
                  <td>
                    <button class="btn-small" onclick="viewInvoice('INV-003')">عرض</button>
                    <button class="btn-small" onclick="printInvoice('INV-003')">طباعة</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Purchases Content (Hidden) -->
      <div id="purchases-content" style="display: none;">
        <h2>🛒 إدارة المشتريات</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>أوامر الشراء</h3>
            <button class="add-btn" onclick="newPurchase()">+ أمر شراء جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>رقم الأمر</th>
                  <th>المورد</th>
                  <th>التاريخ</th>
                  <th>المبلغ</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>PO-001</td>
                  <td>شركة التوريدات المتقدمة</td>
                  <td>2024-01-15</td>
                  <td>15,000 ر.س</td>
                  <td><span class="status-pending">قيد التنفيذ</span></td>
                  <td>
                    <button class="btn-small" onclick="viewPurchase('PO-001')">عرض</button>
                    <button class="btn-small" onclick="receivePurchase('PO-001')">استلام</button>
                  </td>
                </tr>
                <tr>
                  <td>PO-002</td>
                  <td>مؤسسة الجودة للتجارة</td>
                  <td>2024-01-12</td>
                  <td>8,500 ر.س</td>
                  <td><span class="status-paid">مكتمل</span></td>
                  <td>
                    <button class="btn-small" onclick="viewPurchase('PO-002')">عرض</button>
                    <button class="btn-small" onclick="printPurchase('PO-002')">طباعة</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Inventory Content (Hidden) -->
      <div id="inventory-content" style="display: none;">
        <h2>📦 إدارة المخزون</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>المنتجات</h3>
            <button class="add-btn" onclick="addProduct()">+ منتج جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>كود المنتج</th>
                  <th>اسم المنتج</th>
                  <th>الفئة</th>
                  <th>الكمية</th>
                  <th>سعر البيع</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>PRD-001</td>
                  <td>لابتوب ديل</td>
                  <td>إلكترونيات</td>
                  <td>25</td>
                  <td>2,500 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="editProduct('PRD-001')">تعديل</button>
                    <button class="btn-small" onclick="adjustStock('PRD-001')">تعديل المخزون</button>
                  </td>
                </tr>
                <tr>
                  <td>PRD-002</td>
                  <td>طابعة HP</td>
                  <td>إلكترونيات</td>
                  <td>15</td>
                  <td>800 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="editProduct('PRD-002')">تعديل</button>
                    <button class="btn-small" onclick="adjustStock('PRD-002')">تعديل المخزون</button>
                  </td>
                </tr>
                <tr>
                  <td>PRD-003</td>
                  <td>كرسي مكتب</td>
                  <td>أثاث</td>
                  <td>8</td>
                  <td>450 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="editProduct('PRD-003')">تعديل</button>
                    <button class="btn-small" onclick="adjustStock('PRD-003')">تعديل المخزون</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Customers Content (Hidden) -->
      <div id="customers-content" style="display: none;">
        <h2>👥 إدارة العملاء</h2>
        <div class="content-section">
          <div class="section-header">
            <h3>قائمة العملاء</h3>
            <button class="add-btn" onclick="addCustomer()">+ عميل جديد</button>
          </div>
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>كود العميل</th>
                  <th>الاسم</th>
                  <th>الهاتف</th>
                  <th>البريد الإلكتروني</th>
                  <th>إجمالي المشتريات</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>CUS-001</td>
                  <td>أحمد محمد</td>
                  <td>0501234567</td>
                  <td><EMAIL></td>
                  <td>15,500 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="viewCustomer('CUS-001')">عرض</button>
                    <button class="btn-small" onclick="editCustomer('CUS-001')">تعديل</button>
                  </td>
                </tr>
                <tr>
                  <td>CUS-002</td>
                  <td>فاطمة علي</td>
                  <td>0507654321</td>
                  <td><EMAIL></td>
                  <td>8,200 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="viewCustomer('CUS-002')">عرض</button>
                    <button class="btn-small" onclick="editCustomer('CUS-002')">تعديل</button>
                  </td>
                </tr>
                <tr>
                  <td>CUS-003</td>
                  <td>محمد سالم</td>
                  <td>0551234567</td>
                  <td><EMAIL></td>
                  <td>12,800 ر.س</td>
                  <td>
                    <button class="btn-small" onclick="viewCustomer('CUS-003')">عرض</button>
                    <button class="btn-small" onclick="editCustomer('CUS-003')">تعديل</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Reports Content (Hidden) -->
      <div id="reports-content" style="display: none;">
        <h2>📈 التقارير والإحصائيات</h2>

        <div class="content-section">
          <h3>📊 تقارير المبيعات</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="salesReport()">
              <span class="btn-icon">💰</span>
              تقرير المبيعات الشهري
            </button>
            <button class="action-btn" onclick="dailySalesReport()">
              <span class="btn-icon">📅</span>
              تقرير المبيعات اليومي
            </button>
          </div>
        </div>

        <div class="content-section">
          <h3>📦 تقارير المخزون</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="inventoryReport()">
              <span class="btn-icon">📋</span>
              تقرير المخزون الحالي
            </button>
            <button class="action-btn" onclick="lowStockReport()">
              <span class="btn-icon">⚠️</span>
              تقرير المخزون المنخفض
            </button>
          </div>
        </div>

        <div class="content-section">
          <h3>💼 التقارير المالية</h3>
          <div class="action-buttons">
            <button class="action-btn" onclick="profitReport()">
              <span class="btn-icon">💹</span>
              تقرير الأرباح والخسائر
            </button>
            <button class="action-btn" onclick="cashFlowReport()">
              <span class="btn-icon">💵</span>
              تقرير التدفق النقدي
            </button>
          </div>
        </div>

        <div class="content-section">
          <h3>📊 الإحصائيات السريعة</h3>
          <div class="cards">
            <div class="card">
              <div class="card-icon">📈</div>
              <div class="card-title">نمو المبيعات</div>
              <div class="card-value" style="color: #4fc3f7;">+15.5%</div>
              <p>مقارنة بالشهر الماضي</p>
            </div>
            <div class="card">
              <div class="card-icon">👥</div>
              <div class="card-title">عملاء جدد</div>
              <div class="card-value" style="color: #66bb6a;">+28</div>
              <p>هذا الشهر</p>
            </div>
            <div class="card">
              <div class="card-icon">📦</div>
              <div class="card-title">منتجات نشطة</div>
              <div class="card-value" style="color: #ffa726;">156</div>
              <p>في المخزون</p>
            </div>
          </div>
        </div>
      </div>

      <div class="status">
        <strong>✅ النظام يعمل بنجاح!</strong><br>
        جميع الخدمات متاحة ومتصلة بشكل صحيح
      </div>
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <script>
      console.log('🎉 نظام الإدارة المتكامل يعمل بنجاح!');

      // Navigation Functions
      function showDashboard() {
        hideAllContent();
        document.getElementById('dashboard-content').style.display = 'block';
        setActiveNav('dashboard');
        console.log('عرض الداشبورد');
      }

      function showSales() {
        hideAllContent();
        document.getElementById('sales-content').style.display = 'block';
        setActiveNav('sales');
        console.log('عرض صفحة المبيعات');
        alert('مرحباً بك في قسم المبيعات! 💰\n\nهنا يمكنك إدارة جميع فواتير المبيعات والعملاء.');
      }

      function showPurchases() {
        hideAllContent();
        document.getElementById('purchases-content').style.display = 'block';
        setActiveNav('purchases');
        console.log('عرض صفحة المشتريات');
      }

      function showInventory() {
        hideAllContent();
        document.getElementById('inventory-content').style.display = 'block';
        setActiveNav('inventory');
        console.log('عرض صفحة المخزون');
      }

      function showCustomers() {
        hideAllContent();
        document.getElementById('customers-content').style.display = 'block';
        setActiveNav('customers');
        console.log('عرض صفحة العملاء');
      }

      function showReports() {
        hideAllContent();
        document.getElementById('reports-content').style.display = 'block';
        setActiveNav('reports');
        console.log('عرض صفحة التقارير');
      }

      function hideAllContent() {
        const contents = ['dashboard-content', 'sales-content', 'purchases-content', 'inventory-content', 'customers-content', 'reports-content'];
        contents.forEach(id => {
          const element = document.getElementById(id);
          if (element) element.style.display = 'none';
        });
      }

      function setActiveNav(section) {
        document.querySelectorAll('.nav-btn').forEach(btn => {
          btn.classList.remove('active');
        });
        // Set active based on section if needed
      }

      // Quick Action Functions
      function newSale() {
        alert('🎉 إنشاء فاتورة مبيعات جديدة!\n\n✅ سيتم فتح نموذج إنشاء فاتورة جديدة\n📝 يمكنك إضافة المنتجات والعميل\n💰 حساب المجموع تلقائياً');
        console.log('إنشاء فاتورة مبيعات جديدة');
      }

      function newPurchase() {
        alert('🛒 إنشاء أمر شراء جديد!\n\n✅ سيتم فتح نموذج أمر شراء جديد\n📦 يمكنك إضافة المنتجات والمورد\n💵 تتبع حالة الطلب');
        console.log('إنشاء أمر شراء جديد');
      }

      function addProduct() {
        alert('📦 إضافة منتج جديد!\n\n✅ سيتم فتح نموذج إضافة منتج\n🏷️ إدخال اسم المنتج والسعر\n📊 تحديد الفئة والوحدة');
        console.log('إضافة منتج جديد');
      }

      function addCustomer() {
        alert('👤 إضافة عميل جديد!\n\n✅ سيتم فتح نموذج إضافة عميل\n📞 إدخال بيانات الاتصال\n🏠 العنوان ومعلومات إضافية');
        console.log('إضافة عميل جديد');
      }

      // Sales Functions
      function viewInvoice(invoiceId) {
        alert(`📄 عرض الفاتورة: ${invoiceId}\n\n✅ تفاصيل الفاتورة\n📋 قائمة المنتجات\n💰 المبلغ الإجمالي\n👤 بيانات العميل`);
        console.log(`عرض الفاتورة: ${invoiceId}`);
      }

      function printInvoice(invoiceId) {
        alert(`🖨️ طباعة الفاتورة: ${invoiceId}\n\n✅ سيتم فتح نافذة الطباعة\n📄 تنسيق احترافي\n🏢 شعار الشركة`);
        console.log(`طباعة الفاتورة: ${invoiceId}`);
      }

      // Purchase Functions
      function viewPurchase(purchaseId) {
        alert(`📋 عرض أمر الشراء: ${purchaseId}\n\n✅ تفاصيل الأمر\n📦 قائمة المنتجات\n🏢 بيانات المورد\n📅 تاريخ التسليم المتوقع`);
        console.log(`عرض أمر الشراء: ${purchaseId}`);
      }

      function receivePurchase(purchaseId) {
        alert(`📦 استلام أمر الشراء: ${purchaseId}\n\n✅ تأكيد الاستلام\n📊 تحديث المخزون\n✔️ تغيير حالة الأمر إلى مكتمل`);
        console.log(`استلام أمر الشراء: ${purchaseId}`);
      }

      function printPurchase(purchaseId) {
        alert(`🖨️ طباعة أمر الشراء: ${purchaseId}\n\n✅ سيتم فتح نافذة الطباعة\n📄 تنسيق رسمي\n📋 جميع التفاصيل`);
        console.log(`طباعة أمر الشراء: ${purchaseId}`);
      }

      // Inventory Functions
      function editProduct(productId) {
        alert(`✏️ تعديل المنتج: ${productId}\n\n✅ تعديل اسم المنتج\n💰 تحديث الأسعار\n📂 تغيير الفئة\n📝 إضافة وصف`);
        console.log(`تعديل المنتج: ${productId}`);
      }

      function adjustStock(productId) {
        alert(`📊 تعديل مخزون المنتج: ${productId}\n\n✅ إضافة كمية\n➖ خصم كمية\n📝 سبب التعديل\n📅 تاريخ التعديل`);
        console.log(`تعديل مخزون المنتج: ${productId}`);
      }

      // Customer Functions
      function viewCustomer(customerId) {
        alert(`👤 عرض بيانات العميل: ${customerId}\n\n✅ المعلومات الشخصية\n📞 بيانات الاتصال\n💰 تاريخ المشتريات\n📊 إحصائيات العميل`);
        console.log(`عرض بيانات العميل: ${customerId}`);
      }

      function editCustomer(customerId) {
        alert(`✏️ تعديل بيانات العميل: ${customerId}\n\n✅ تحديث المعلومات\n📞 تعديل الهاتف\n📧 تحديث البريد الإلكتروني\n🏠 تعديل العنوان`);
        console.log(`تعديل بيانات العميل: ${customerId}`);
      }

      // Reports Functions
      function salesReport() {
        alert('📊 تقرير المبيعات الشهري\n\n✅ إجمالي المبيعات: 125,430 ر.س\n📈 نمو 15.5% عن الشهر الماضي\n🏆 أفضل منتج: لابتوب ديل\n📅 الفترة: يناير 2024');
        console.log('عرض تقرير المبيعات الشهري');
      }

      function dailySalesReport() {
        alert('📅 تقرير المبيعات اليومي\n\n✅ مبيعات اليوم: 25,430 ر.س\n📋 عدد الفواتير: 12\n👥 عدد العملاء: 8\n⭐ متوسط قيمة الفاتورة: 2,119 ر.س');
        console.log('عرض تقرير المبيعات اليومي');
      }

      function inventoryReport() {
        alert('📋 تقرير المخزون الحالي\n\n✅ إجمالي قيمة المخزون: 125,800 ر.س\n📦 عدد المنتجات: 156\n⚠️ منتجات منخفضة المخزون: 5\n📈 أسرع المنتجات مبيعاً: لابتوب ديل');
        console.log('عرض تقرير المخزون الحالي');
      }

      function lowStockReport() {
        alert('⚠️ تقرير المخزون المنخفض\n\n🔴 منتجات تحتاج إعادة طلب:\n• كرسي مكتب (8 قطع)\n• ماوس لاسلكي (3 قطع)\n• لوحة مفاتيح (6 قطع)\n• سماعات (4 قطع)\n• كابل USB (2 قطع)');
        console.log('عرض تقرير المخزون المنخفض');
      }

      function profitReport() {
        alert('💹 تقرير الأرباح والخسائر\n\n✅ إجمالي الإيرادات: 125,430 ر.س\n💰 إجمالي التكاليف: 89,500 ر.س\n🎯 صافي الربح: 35,930 ر.س\n📊 هامش الربح: 28.6%');
        console.log('عرض تقرير الأرباح والخسائر');
      }

      function cashFlowReport() {
        alert('💵 تقرير التدفق النقدي\n\n✅ النقد الداخل: 125,430 ر.س\n💸 النقد الخارج: 89,500 ر.س\n💰 صافي التدفق النقدي: +35,930 ر.س\n🏦 الرصيد النقدي: 245,680 ر.س');
        console.log('عرض تقرير التدفق النقدي');
      }

      // إضافة تأثيرات تفاعلية
      document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.card').forEach(card => {
          card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
          });
          card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
          });
        });
      });

      // تحديث الوقت
      setInterval(() => {
        const now = new Date();
        const timeStr = now.toLocaleTimeString('ar-SA');
        document.title = `نظام الإدارة المتكامل - ${timeStr}`;
      }, 1000);

      // رسالة ترحيب
      setTimeout(() => {
        console.log('مرحباً بك في نظام الإدارة المتكامل! 🚀');
        alert('🎉 مرحباً بك في نظام الإدارة المتكامل!\n\n✅ النظام يعمل بكامل مميزاته\n🖱️ انقر على أي زر للتنقل\n📊 استخدم الداشبورد لمتابعة الإحصائيات\n🚀 جرب الإجراءات السريعة');
      }, 2000);
    </script>
  </body>
</html>

<!doctype html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>نظام الإدارة المتكامل</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: white;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.1);
        padding: 30px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }
      h1 {
        text-align: center;
        font-size: 3em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      }
      .subtitle {
        text-align: center;
        font-size: 1.2em;
        margin-bottom: 40px;
        opacity: 0.9;
      }
      .cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin: 40px 0;
      }
      .card {
        background: rgba(255, 255, 255, 0.2);
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
        cursor: pointer;
      }
      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4);
      }
      .card-icon {
        font-size: 3em;
        margin-bottom: 15px;
      }
      .card-title {
        font-size: 1.3em;
        margin-bottom: 10px;
        font-weight: bold;
      }
      .card-value {
        font-size: 2.2em;
        font-weight: bold;
        margin: 15px 0;
      }
      .status {
        background: rgba(76, 175, 80, 0.3);
        border: 2px solid #4caf50;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        margin-top: 30px;
        font-size: 1.1em;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎉 نظام الإدارة المتكامل</h1>
      <p class="subtitle">نظام شامل لإدارة الأعمال والمؤسسات</p>

      <div class="cards">
        <div class="card">
          <div class="card-icon">📊</div>
          <div class="card-title">المبيعات اليومية</div>
          <div class="card-value" style="color: #4fc3f7;">25,430 ر.س</div>
          <p>إجمالي مبيعات اليوم</p>
        </div>

        <div class="card">
          <div class="card-icon">🛒</div>
          <div class="card-title">المشتريات</div>
          <div class="card-value" style="color: #ff7043;">18,250 ر.س</div>
          <p>إجمالي المشتريات</p>
        </div>

        <div class="card">
          <div class="card-icon">📦</div>
          <div class="card-title">المخزون</div>
          <div class="card-value" style="color: #66bb6a;">125,800 ر.س</div>
          <p>قيمة المخزون الحالي</p>
        </div>

        <div class="card">
          <div class="card-icon">👥</div>
          <div class="card-title">العملاء</div>
          <div class="card-value" style="color: #ffa726;">1,245</div>
          <p>عدد العملاء المسجلين</p>
        </div>
      </div>

      <div class="status">
        <strong>✅ النظام يعمل بنجاح!</strong><br>
        جميع الخدمات متاحة ومتصلة بشكل صحيح
      </div>
    </div>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <script>
      console.log('🎉 نظام الإدارة المتكامل يعمل بنجاح!');

      // إضافة تأثيرات تفاعلية
      document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('click', function() {
          this.style.transform = 'scale(0.95)';
          setTimeout(() => {
            this.style.transform = 'translateY(-5px)';
          }, 150);
        });
      });

      // تحديث الوقت
      setInterval(() => {
        const now = new Date();
        const timeStr = now.toLocaleTimeString('ar-SA');
        document.title = `نظام الإدارة المتكامل - ${timeStr}`;
      }, 1000);

      // رسالة ترحيب
      setTimeout(() => {
        console.log('مرحباً بك في نظام الإدارة المتكامل! 🚀');
        alert('مرحباً بك في نظام الإدارة المتكامل! 🎉');
      }, 2000);
    </script>
  </body>
</html>

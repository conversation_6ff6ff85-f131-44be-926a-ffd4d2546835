import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Product, Category, Unit, ApiResponse, PaginatedResponse } from '../../types';

interface ProductsState {
  products: Product[];
  categories: Category[];
  units: Unit[];
  loading: boolean;
  error: string | null;
  selectedProduct: Product | null;
  filters: {
    search: string;
    categoryId: string;
    isActive: boolean | null;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: ProductsState = {
  products: [],
  categories: [],
  units: [],
  loading: false,
  error: null,
  selectedProduct: null,
  filters: {
    search: '',
    categoryId: '',
    isActive: null,
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
  },
};

// Async thunks
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async (params: { page?: number; limit?: number; search?: string; categoryId?: string }) => {
    // Mock API call - replace with actual API
    const mockProducts: Product[] = [
      {
        id: '1',
        code: 'P001',
        name: 'منتج تجريبي 1',
        description: 'وصف المنتج التجريبي الأول',
        categoryId: 'cat1',
        unitId: 'unit1',
        price: 100,
        cost: 80,
        minStock: 10,
        maxStock: 100,
        currentStock: 50,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2',
        code: 'P002',
        name: 'منتج تجريبي 2',
        description: 'وصف المنتج التجريبي الثاني',
        categoryId: 'cat2',
        unitId: 'unit2',
        price: 200,
        cost: 160,
        minStock: 5,
        maxStock: 50,
        currentStock: 25,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    return {
      data: mockProducts,
      total: mockProducts.length,
      page: params.page || 1,
      limit: params.limit || 10,
      totalPages: Math.ceil(mockProducts.length / (params.limit || 10)),
    } as PaginatedResponse<Product>;
  }
);

export const fetchCategories = createAsyncThunk(
  'products/fetchCategories',
  async () => {
    // Mock API call
    const mockCategories: Category[] = [
      { id: 'cat1', name: 'فئة 1', description: 'وصف الفئة الأولى' },
      { id: 'cat2', name: 'فئة 2', description: 'وصف الفئة الثانية' },
    ];
    return mockCategories;
  }
);

export const fetchUnits = createAsyncThunk(
  'products/fetchUnits',
  async () => {
    // Mock API call
    const mockUnits: Unit[] = [
      { id: 'unit1', name: 'قطعة', symbol: 'قطعة' },
      { id: 'unit2', name: 'كيلوجرام', symbol: 'كجم' },
      { id: 'unit3', name: 'متر', symbol: 'م' },
    ];
    return mockUnits;
  }
);

export const createProduct = createAsyncThunk(
  'products/createProduct',
  async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    // Mock API call
    const newProduct: Product = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    return newProduct;
  }
);

export const updateProduct = createAsyncThunk(
  'products/updateProduct',
  async (productData: Product) => {
    // Mock API call
    const updatedProduct: Product = {
      ...productData,
      updatedAt: new Date(),
    };
    return updatedProduct;
  }
);

export const deleteProduct = createAsyncThunk(
  'products/deleteProduct',
  async (productId: string) => {
    // Mock API call
    return productId;
  }
);

const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setSelectedProduct: (state, action: PayloadAction<Product | null>) => {
      state.selectedProduct = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<ProductsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action: PayloadAction<Partial<ProductsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch products
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.data;
        state.pagination = {
          page: action.payload.page,
          limit: action.payload.limit,
          total: action.payload.total,
        };
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'فشل في تحميل المنتجات';
      })
      // Fetch categories
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload;
      })
      // Fetch units
      .addCase(fetchUnits.fulfilled, (state, action) => {
        state.units = action.payload;
      })
      // Create product
      .addCase(createProduct.fulfilled, (state, action) => {
        state.products.push(action.payload);
      })
      // Update product
      .addCase(updateProduct.fulfilled, (state, action) => {
        const index = state.products.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.products[index] = action.payload;
        }
      })
      // Delete product
      .addCase(deleteProduct.fulfilled, (state, action) => {
        state.products = state.products.filter(p => p.id !== action.payload);
      });
  },
});

export const { setSelectedProduct, setFilters, setPagination, clearError } = productsSlice.actions;
export default productsSlice.reducer;
